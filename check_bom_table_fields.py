#!/usr/bin/python3
"""
检查飞书BOM表格的字段结构
用于确认实际的字段名称，避免字段名不匹配的错误
"""

import json
import config
import vehicle_sw_version_table
from log_config import logger


def check_bom_table_structure():
    """检查BOM表格的字段结构"""
    
    print("=" * 80)
    print("飞书BOM表格字段结构检查")
    print("=" * 80)
    
    try:
        # 初始化BOM表格
        bom_table = vehicle_sw_version_table.VehicleBom()
        
        # 获取表格的所有记录（只取前几条用于分析字段结构）
        print("正在获取BOM表格数据...")
        records = []
        count = 0
        for record in bom_table.get_all_records():
            records.append(record)
            count += 1
            if count >= 3:  # 只取前3条记录分析
                break
        
        if not records:
            print("❌ BOM表格中没有数据，无法分析字段结构")
            print("建议：先手动在飞书表格中添加一条示例数据")
            return
        
        print(f"✅ 获取到 {len(records)} 条记录用于分析")
        
        # 分析字段结构
        print("\n" + "=" * 80)
        print("字段结构分析")
        print("=" * 80)
        
        for i, record in enumerate(records):
            print(f"\n📋 记录 {i+1}:")
            print(f"记录ID: {record.get('record_id')}")
            
            fields = record.get('fields', {})
            print(f"字段数量: {len(fields)}")
            
            print("\n字段详情:")
            for field_name, field_value in fields.items():
                # 分析字段值的类型和格式
                value_type = type(field_value).__name__
                
                if isinstance(field_value, list) and len(field_value) > 0:
                    if isinstance(field_value[0], dict) and 'text' in field_value[0]:
                        # 文本字段格式
                        actual_value = field_value[0].get('text', '')
                        print(f"  📝 {field_name:20} | 文本字段    | {actual_value}")
                    else:
                        print(f"  📋 {field_name:20} | 列表字段    | {field_value}")
                elif isinstance(field_value, dict):
                    print(f"  📊 {field_name:20} | 对象字段    | {field_value}")
                else:
                    print(f"  📄 {field_name:20} | {value_type:10} | {field_value}")
        
        # 提取所有唯一字段名
        all_field_names = set()
        for record in records:
            fields = record.get('fields', {})
            all_field_names.update(fields.keys())
        
        print("\n" + "=" * 80)
        print("所有字段名汇总")
        print("=" * 80)
        
        sorted_fields = sorted(all_field_names)
        for i, field_name in enumerate(sorted_fields, 1):
            print(f"{i:2d}. {field_name}")
        
        # 生成字段映射建议
        print("\n" + "=" * 80)
        print("字段映射建议")
        print("=" * 80)
        
        # 预期的字段名（基于TVAS数据结构）
        expected_fields = [
            "vin", "part_number", "part_revision", "en_name", "cn_name",
            "quantity", "uom", "partition_code", "creator", "create_time",
            "modifier", "modify_time", "status"
        ]
        
        print("预期字段 → 实际字段映射:")
        for expected in expected_fields:
            # 查找最匹配的实际字段名
            matches = [f for f in sorted_fields if expected.lower() in f.lower() or f.lower() in expected.lower()]
            if matches:
                print(f"  {expected:15} → {matches[0]}")
            else:
                print(f"  {expected:15} → ❌ 未找到匹配字段")
        
        print("\n实际字段中未匹配的:")
        unmatched = []
        for actual in sorted_fields:
            found = False
            for expected in expected_fields:
                if expected.lower() in actual.lower() or actual.lower() in expected.lower():
                    found = True
                    break
            if not found:
                unmatched.append(actual)
        
        for field in unmatched:
            print(f"  ❓ {field}")
        
        # 生成代码建议
        print("\n" + "=" * 80)
        print("代码修复建议")
        print("=" * 80)
        
        print("基于分析结果，建议的字段映射代码:")
        print("```python")
        print("formatted_record = {")
        for expected in expected_fields:
            matches = [f for f in sorted_fields if expected.lower() in f.lower() or f.lower() in expected.lower()]
            if matches:
                actual_field = matches[0]
                if expected in ["create_time", "modify_time"]:
                    print(f'    "{actual_field}": {expected}_str,  # 时间字段，使用格式化字符串')
                elif expected == "quantity":
                    print(f'    "{actual_field}": str(bom_record.get("{expected}", "")),  # 数量字段，转为字符串')
                else:
                    print(f'    "{actual_field}": bom_record.get("{expected}", ""),')
            else:
                print(f'    # "{expected}": bom_record.get("{expected}", ""),  # ❌ 字段未找到')
        print("}")
        print("```")
        
    except Exception as e:
        print(f"❌ 检查BOM表格结构失败: {str(e)}")
        logger.error(f"检查BOM表格结构失败: {str(e)}")


def test_field_mapping():
    """测试字段映射"""
    
    print("\n" + "=" * 80)
    print("字段映射测试")
    print("=" * 80)
    
    # 模拟TVAS数据
    sample_tvas_data = {
        "id": 4093440,
        "vin": "HJNAAJNC8SA050133",
        "part_number": "S0000979",
        "part_revision": "AA",
        "en_name": "Bolt, M10x1.5x25",
        "cn_name": "六角法兰面螺栓",
        "quantity": "4",
        "uom": "PC",
        "partition_code": "STD.00.00.00",
        "creator": "VINBOM",
        "create_time": 1754543582,
        "modifier": "VINBOM",
        "modify_time": 1754543582,
        "status": "Assembled"
    }
    
    print("📋 TVAS原始数据:")
    print(json.dumps(sample_tvas_data, ensure_ascii=False, indent=2))
    
    # 转换时间戳
    import datetime
    
    create_time_str = ""
    modify_time_str = ""
    
    if sample_tvas_data.get("create_time"):
        dt = datetime.datetime.fromtimestamp(int(sample_tvas_data["create_time"]))
        create_time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
    
    if sample_tvas_data.get("modify_time"):
        dt = datetime.datetime.fromtimestamp(int(sample_tvas_data["modify_time"]))
        modify_time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
    
    # 格式化后的数据
    formatted_data = {
        "vin": sample_tvas_data.get("vin", ""),
        "part_number": sample_tvas_data.get("part_number", ""),
        "part_revision": sample_tvas_data.get("part_revision", ""),
        "en_name": sample_tvas_data.get("en_name", ""),
        "cn_name": sample_tvas_data.get("cn_name", ""),
        "quantity": str(sample_tvas_data.get("quantity", "")),
        "uom": sample_tvas_data.get("uom", ""),
        "partition_code": sample_tvas_data.get("partition_code", ""),
        "creator": sample_tvas_data.get("creator", ""),
        "create_time": create_time_str,
        "modifier": sample_tvas_data.get("modifier", ""),
        "modify_time": modify_time_str,
        "status": sample_tvas_data.get("status", "")
    }
    
    print("\n📝 格式化后的数据:")
    print(json.dumps(formatted_data, ensure_ascii=False, indent=2))
    
    print(f"\n⏰ 时间戳转换示例:")
    print(f"原始时间戳: {sample_tvas_data['create_time']}")
    print(f"格式化时间: {create_time_str}")


def main():
    """主函数"""
    print("开始检查飞书BOM表格字段结构...")
    
    # 检查表格结构
    check_bom_table_structure()
    
    # 测试字段映射
    test_field_mapping()
    
    print("\n" + "=" * 80)
    print("检查完成")
    print("=" * 80)
    print("请根据上述分析结果修改代码中的字段映射")


if __name__ == "__main__":
    main()
