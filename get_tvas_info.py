import requests
from typing import Optional

from log_config import logger


#从 TVAS 获取当前数据，NT2.0/NT2.5可用，NT3.0 不全

class GetTvasInfo:
    def __init__(self, cookie: dict):
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }

    # """
    # query_by_vin 方法：获取车辆基础信息
    # 通过车辆 vin 码，调用 https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_by_vin 接口，获取车辆基础信息（如车型、品牌、生产时间、所属部门等）。
    
    # {
    # "result_code": "success",
    # "message": "ok",
    # "data": [
    #     {
    #         "id": 1152,
    #         "vehicle_code": "PP-GEM006-CN",
    #         "vin": "LJ1E6A2U7NG036000",
    #         "type": "PHY",
    #         "brand": "NIO",
    #         "vehicle_model": "Gemini",
    #         "vehicle_generation": "G1.1",
    #         "manufacture_stage": "PP",
    #         "sales_region": "CN",
    #         "vehicle_product_date": 1654099200,
    #         "vehicle_status": "veh_occupy",
    #         "purchase_department_id": "SUPERVISORY_ORGANIZATION-3-3944",
    #         "purchase_department_name": "AD",
    #         "belonging_department_id": "SUPERVISORY_ORGANIZATION-3-3944",
    #         "belonging_department_name": "AD",
    #         "test_label": "NIO-All team",
    #         "platform": "NT2.0",
    #         "responsible_person": "wei.tian3.o",
    #         "responsible_depart_tier1_id": "SUPERVISORY_ORGANIZATION-3-3944",
    #         "responsible_depart_tier1_abbr_name": "AD",
    #         "responsible_depart_tier2_id": "SUPERVISORY_ORGANIZATION-3-6145",
    #         "responsible_depart_tier2_abbr_name": "ADT",
    #         "vehicle_position": "上海市-上海市-嘉定区",
    #         "identification_code": "2047",
    #         "reservation_allowed": 1,
    #         "vehicle_model_code": "VEHICLE-0015"
    #     }
    # ],
    # "request_id": "mk-AGyWUBZokbXlASsBbq8",
    # "path": "/v1/vehicles/query_by_vin",
    # "server_time": 1754379749,
    # "tx_id": "6952ff266e3f4512bb7764b9f8ab47be"
    # }
    # """

    def query_by_vin(self, vin: str) -> Optional[dict]:
        self.method = "GET"
        self.url = f"https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_by_vin?vin={vin}"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("message") == "ok":
                data_list = response_dict.get("data")
                if data_list and len(data_list) > 0:
                    data_dict = data_list[0]
                    return data_dict
                else:
                    logger.warning(f"No vehicle info found for {vin}, please check that the vin is correct!")
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    # """
    # query_ecu_version 方法：获取车辆 ECU 版本及相关数据
    # 通过车辆 vin 码，调用 https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_ecu_version 接口，获取车辆 ECU 版本、车辆位置、电池信息、各 ECU 模块的软硬件版本 等数据。
    # {
    # "result_code": "success",
    # "message": "ok",
    # "data": {
    #     "mileage": "17809",
    #     "battery_cap_type": "P100 NCM",
    #     "vehicle_id": "9a5a6ff0c4074c20872fe98295dd9b91",
    #     "package_global_version": "ES7.G1.3.AJ.01_6aeea1*3.2.0build34",
    #     "province": "上海市",
    #     "city": "上海市",
    #     "district": "嘉定区",
    #     "latitude": "31.282635",
    #     "longitude": "121.170154",
    #     "posng_valid_type": 0,
    #     "env": "staging",
    #     "soc": "3.0",
    #     "battery_id": "P0205908AQ13022V218952L12A00095",
    #     "sample_time": "1754376817",
    #     "soc_sample_time": "1754376817",
    #     "ecu_version_map": {
    #         "ADC": {
    #             "ecu": "ADC",
    #             "software_pn": "P0228609 MQ",
    #             "hardware_pn": "P0211450 AJ"
    #         },
    #         "CDC": {
    #             "ecu": "CDC",
    #             "software_pn": "P0226475 OY",
    #             "hardware_pn": "P0212056 AE"
    #         },
    #         "EPS1": {
    #             "ecu": "EPS1",
    #             "software_pn": "P0247568 AH",
    #             "hardware_pn": "P0248185 AA"
    #         },
    #         "EPS2": {
    #             "ecu": "EPS2",
    #             "software_pn": "P0247568 AH",
    #             "hardware_pn": "P0248185 AA"
    #         },
    #         "DHS_RL": {
    #             "ecu": "DHS_RL",
    #             "software_pn": "P0236595 AC",
    #             "hardware_pn": "P0233767 AB"
    #         },
    #         "WLC": {
    #             "ecu": "WLC",
    #             "software_pn": "P0236051 AW",
    #             "hardware_pn": "P0241642 AC"
    #         },
    #         "DHS_FL": {
    #             "ecu": "DHS_FL",
    #             "software_pn": "P0236595 AC",
    #             "hardware_pn": "P0233765 AB"
    #         },
    #         "RLM_CM": {
    #             "ecu": "RLM_CM",
    #             "software_pn": "P0249022 AG",
    #             "hardware_pn": "P0217169 AF"
    #         },
    #         "LBM": {
    #             "ecu": "LBM",
    #             "software_pn": "P0228608 BR",
    #             "hardware_pn": "P0218039 AG"
    #         },
    #         "DHS_RR": {
    #             "ecu": "DHS_RR",
    #             "software_pn": "P0236595 AC",
    #             "hardware_pn": "P0233768 AB"
    #         },
    #         "IC": {
    #             "ecu": "IC",
    #             "software_pn": "P0222172 AI",
    #             "hardware_pn": "P0211318 AH"
    #         },
    #         "DHS_FR": {
    #             "ecu": "DHS_FR",
    #             "software_pn": "P0236595 AC",
    #             "hardware_pn": "P0233766 AB"
    #         },
    #         "DCM_FR": {
    #             "ecu": "DCM_FR",
    #             "software_pn": "P0233164 BA",
    #             "hardware_pn": "P0211248 AE"
    #         },
    #         "AMP": {
    #             "ecu": "AMP",
    #             "software_pn": "P0226331 BA",
    #             "hardware_pn": "P0211507 AG"
    #         },
    #         "FLM_R": {
    #             "ecu": "FLM_R",
    #             "software_pn": "P0236032 AS",
    #             "hardware_pn": "P0218098 AJ"
    #         },
    #         "SCU_P": {
    #             "ecu": "SCU_P",
    #             "software_pn": "P0223241 BA",
    #             "hardware_pn": "P0212015 AB"
    #         },
    #         "DCM_FL": {
    #             "ecu": "DCM_FL",
    #             "software_pn": "P0233163 BA",
    #             "hardware_pn": "P0211247 AE"
    #         },
    #         "FLM_L": {
    #             "ecu": "FLM_L",
    #             "software_pn": "P0236032 AS",
    #             "hardware_pn": "P0218096 AJ"
    #         },
    #         "BCM": {
    #             "ecu": "BCM",
    #             "software_pn": "P0235928 BN",
    #             "hardware_pn": "P0211968 AC"
    #         },
    #         "DCM_RR": {
    #             "ecu": "DCM_RR",
    #             "software_pn": "P0233167 BA",
    #             "hardware_pn": "P0211252 AE"
    #         },
    #         "HVDI": {
    #             "ecu": "HVDI",
    #             "software_pn": "P0236162 AM",
    #             "hardware_pn": "P0221716 AG"
    #         },
    #         "BGW": {
    #             "ecu": "BGW",
    #             "software_pn": "P0218754 MS",
    #             "hardware_pn": "P0216753 AE"
    #         },
    #         "RLM_LS": {
    #             "ecu": "RLM_LS",
    #             "software_pn": "P0249023 AG",
    #             "hardware_pn": "P0217167 AF"
    #         },
    #         "DCM_RL": {
    #             "ecu": "DCM_RL",
    #             "software_pn": "P0233166 BA",
    #             "hardware_pn": "P0211249 AE"
    #         },
    #         "BCU": {
    #             "ecu": "BCU",
    #             "software_pn": "P0224263 BN",
    #             "hardware_pn": "P0213114 AC"
    #         },
    #         "NOMI": {
    #             "ecu": "NOMI",
    #             "software_pn": "P0089261 AU",
    #             "hardware_pn": "P0076652 AJ"
    #         },
    #         "SCU_D": {
    #             "ecu": "SCU_D",
    #             "software_pn": "P0223240 BA",
    #             "hardware_pn": "P0212011 AB"
    #         },
    #         "VCU": {
    #             "ecu": "VCU",
    #             "software_pn": "P0216819 LF",
    #             "hardware_pn": "P0213123 AC"
    #         },
    #         "PLG": {
    #             "ecu": "PLG",
    #             "software_pn": "P0232580 AR",
    #             "hardware_pn": "P0218382 AD"
    #         },
    #         "SA": {
    #             "ecu": "SA",
    #             "software_pn": "P0218511 HS",
    #             "hardware_pn": "P0216670 AG"
    #         },
    #         "SCU_RL": {
    #             "ecu": "SCU_RL",
    #             "software_pn": "P0237948 AE",
    #             "hardware_pn": "P0237415 AA"
    #         },
    #         "PEU_F": {
    #             "ecu": "PEU_F",
    #             "software_pn": "P0256988 CG",
    #             "hardware_pn": "P0224192 AE"
    #         },
    #         "HUD": {
    #             "ecu": "HUD",
    #             "software_pn": "P0237420 AG",
    #             "hardware_pn": "P0223025 AE"
    #         },
    #         "PEU_R": {
    #             "ecu": "PEU_R",
    #             "software_pn": "P0237449 BY",
    #             "hardware_pn": "P0237381 AB"
    #         },
    #         "SWC": {
    #             "ecu": "SWC",
    #             "software_pn": "P0270261 AF",
    #             "hardware_pn": "P0269938 AB"
    #         },
    #         "RAD_RL": {
    #             "ecu": "RAD_RL",
    #             "software_pn": "P0218515 AN",
    #             "hardware_pn": "P0215618 AD"
    #         },
    #         "RAD_FC": {
    #             "ecu": "RAD_FC",
    #             "software_pn": "P0218514 AL",
    #             "hardware_pn": "P0212898 AD"
    #         },
    #         "BMS": {
    #             "ecu": "BMS",
    #             "software_pn": "P0208587 EM",
    #             "hardware_pn": "P0208909 AB"
    #         },
    #         "FCC1": {
    #             "ecu": "FCC1",
    #             "software_pn": "P0250880 CG",
    #             "hardware_pn": "P0223461 AC"
    #         },
    #         "RAD_RR": {
    #             "ecu": "RAD_RR",
    #             "software_pn": "P0218515 AN",
    #             "hardware_pn": "P0215618 AD"
    #         },
    #         "ACM": {
    #             "ecu": "ACM",
    #             "software_pn": "P0237020 AK",
    #             "hardware_pn": "P0064885 AH"
    #         },
    #         "FCC2": {
    #             "ecu": "FCC2",
    #             "software_pn": "P0250882 BH",
    #             "hardware_pn": "P0223460 AC"
    #         },
    #         "RLM_RS": {
    #             "ecu": "RLM_RS",
    #             "software_pn": "P0249024 AG",
    #             "hardware_pn": "P0217168 AF"
    #         },
    #         "RAD_FL": {
    #             "ecu": "RAD_FL",
    #             "software_pn": "P0218515 AN",
    #             "hardware_pn": "P0215618 AD"
    #         },
    #         "APA": {
    #             "ecu": "APA",
    #             "software_pn": "P0218516 BR",
    #             "hardware_pn": "P0224190 AA"
    #         },
    #         "CCU": {
    #             "ecu": "CCU",
    #             "software_pn": "P0214710 BI",
    #             "hardware_pn": "P0214702 AD"
    #         },
    #         "FI_R": {
    #             "ecu": "FI_R",
    #             "software_pn": "P0227165 AJ",
    #             "hardware_pn": "P0212084 AF"
    #         },
    #         "RAD_FR": {
    #             "ecu": "RAD_FR",
    #             "software_pn": "P0218515 AN",
    #             "hardware_pn": "P0215618 AD"
    #         },
    #         "ICS": {
    #             "ecu": "ICS",
    #             "software_pn": "P0237149 AL",
    #             "hardware_pn": "P0238925 AD"
    #         },
    #         "LID_MAIN": {
    #             "ecu": "LID_MAIN",
    #             "software_pn": "P0235753 DW",
    #             "hardware_pn": "P0229800 AI"
    #         },
    #         "BTA5": {
    #             "ecu": "BTA5",
    #             "software_pn": "P0255477 DF",
    #             "hardware_pn": "P0254896 AC"
    #         },
    #         "BTA4": {
    #             "ecu": "BTA4",
    #             "software_pn": "P0228490 DF",
    #             "hardware_pn": "P0211967 AG"
    #         },
    #         "BTA3": {
    #             "ecu": "BTA3",
    #             "software_pn": "P0228489 DF",
    #             "hardware_pn": "P0211966 AG"
    #         },
    #         "SCM": {
    #             "ecu": "SCM",
    #             "software_pn": "P0061551 AQ",
    #             "hardware_pn": "P0055154 AE"
    #         },
    #         "BTA2": {
    #             "ecu": "BTA2",
    #             "software_pn": "P0228488 DF",
    #             "hardware_pn": "P0211965 AG"
    #         },
    #         "BTA1": {
    #             "ecu": "BTA1",
    #             "software_pn": "P0228463 DF",
    #             "hardware_pn": "P0211964 AI"
    #         }
    #     }
    # },
    # "request_id": "mk-AGyWWmFokap-A7UDvtY",
    # "path": "/v1/vehicles/query_ecu_version",
    # "server_time": 1754376831,
    # "tx_id": "636f033fad0c40d5a490e8874b2dfd9c"
    # }

    # {
    # "sample_time": data_dict.get("sample_time"),
    # "vehicle_id": data_dict.get("vehicle_id"),
    # "package_global_version": data_dict.get("package_global_version"),
    # "ecu_version_map": data_dict.get("ecu_version_map", {}),
    # "env": data_dict.get("env"),
    # }

    # 即只保留 采样时间、车辆 ID、全局版本、ECU 版本映射、环境信息，其他字段（如 mileage、battery_cap_type 等）会被过滤掉。
    # Optional[dict] : 要么返回字典要么返回空！
    # """

    def query_ecu_version(self, vin: str) -> Optional[dict]:
        self.method = "GET"
        self.url = f"https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/query_ecu_version?vin={vin}"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("message") == "ok":
                data_dict = response_dict.get("data")
                if data_dict:
                    result_dict = {
                        "sample_time": data_dict.get("sample_time"),
                        "vehicle_id": data_dict.get("vehicle_id"),
                        "package_global_version": data_dict.get("package_global_version"),
                        "ecu_version_map": data_dict.get("ecu_version_map", {}),
                        "env": data_dict.get("env"),
                    }
                    return result_dict
                else:
                    logger.warning(f"No ecu_version data for {vin}")
                    logger.debug(response_dict)
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    def query_bom_by_vin(self, vin: str, page_size: int = 50, page_count: int = 1) -> Optional[dict]:
        """
        通过VIN获取车辆BOM（物料清单）信息
        :param vin: 车辆识别码
        :param page_size: 每页数量，默认50
        :param page_count: 页码，默认1
        :return: BOM信息字典或None
        """
        self.method = "POST"
        self.url = "https://request-proxy.nioint.com/proxy/tvas/v1/vehicles/bom/paginate"
        self.payload = {
            "vin": vin,
            "page_size": page_size,
            "page_count": page_count
        }

        import json
        response = requests.request(
            self.method,
            self.url,
            headers=self.headers,
            data=json.dumps(self.payload)
        )

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("message") == "ok":
                data_dict = response_dict.get("data")
                if data_dict:
                    return data_dict
                else:
                    logger.warning(f"No BOM data found for {vin}")
                    return None
            else:
                logger.error(f"BOM query failed for {vin}: {response_dict}")
                return None
        else:
            logger.error(f"BOM query request failed for {vin}: {response.text}")
            return None

    def get_all_bom_data(self, vin: str) -> list:
        """
        获取指定VIN的所有BOM数据（处理分页）
        :param vin: 车辆识别码
        :return: 所有BOM记录的列表
        """
        all_bom_records = []
        page_count = 1
        page_size = 50

        while True:
            bom_data = self.query_bom_by_vin(vin, page_size, page_count)
            if not bom_data:
                break

            records = bom_data.get("list", [])
            if not records:
                break

            all_bom_records.extend(records)

            # 检查是否还有更多数据
            total_count = bom_data.get("total_count", 0)
            current_count = len(all_bom_records)

            logger.info(f"VIN {vin}: 已获取 {current_count}/{total_count} 条BOM记录")

            if current_count >= total_count:
                break

            page_count += 1

        logger.info(f"VIN {vin}: 总共获取 {len(all_bom_records)} 条BOM记录")
        return all_bom_records
