#!/usr/bin/python3
"""
BOM数据同步脚本
独立运行BOM数据同步功能，从TVAS系统获取车辆BOM信息并同步到飞书表格
"""
import re
import datetime
import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import config
import login_nio
import get_tvas_info
import vehicle_sw_version_table
import robot_request
from lark_requests import VehicleBom 
from log_config import logger


def convert_bom_record_with_mapping(tvas_record, vin):
    """
    使用字段映射转换BOM记录格式，未匹配到轮胎规格时VIN设为空
    :param tvas_record: TVAS原始BOM记录
    :param vin: 车辆识别码
    :return: 格式化后的记录
    """
    # 检查当前记录是否匹配到轮胎规格（用于决定是否设置空VIN）
    tire_pattern = re.compile(r'\d{3}/\d{2}\s*R\d{2}')
    has_valid_tire_spec = False
    if "en_name" in tvas_record:
        if tire_pattern.search(tvas_record["en_name"]):
            has_valid_tire_spec = True

    # BOM字段映射（基于飞书表格实际字段）
    field_mapping = {
        "vin": "vin",
        "part_number": "part_number",
        "en_name": "en_name",
        "cn_name": "cn_name",
        "quantity": "quantity",
        "uom": "uom",
        "partition_code": "partition_code",
        "creator": "creator",
        "create_time": "create_time",
        "modifier": "modifier",
        "modify_time": "modify_time",
        "status": "status"
    }

    def format_timestamp(timestamp):
        """格式化时间戳"""
        if not timestamp:
            return ""
        try:
            dt = datetime.datetime.fromtimestamp(int(timestamp))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError) as e:
            logger.warning(f"时间戳转换失败: {timestamp}, 错误: {e}")
            return str(timestamp)

    # 构建格式化记录
    formatted_record = {}

    for tvas_field, feishu_field in field_mapping.items():
        if tvas_field == "vin":
            formatted_record[feishu_field] = vin  # 使用处理后的vin（可能为空）
        elif tvas_field == "create_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("create_time"))
        elif tvas_field == "modify_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("modify_time"))
        elif tvas_field == "quantity":
            # 确保数量为字符串
            formatted_record[feishu_field] = str(tvas_record.get(tvas_field, ""))
        else:
            value = tvas_record.get(tvas_field, "")
            formatted_record[feishu_field] = str(value) if value is not None else ""

    return formatted_record


def sync_bom_data_for_vehicle(vin: str, tvas_client) -> list:
    """
    同步单个车辆的BOM数据到飞书表格
    :param vin: 车辆识别码
    :param tvas_client: TVAS客户端实例
    :return: BOM记录列表
    """
    try:
        logger.info(f"开始同步VIN {vin} 的BOM数据")
        
        # 获取BOM数据（已处理去重）
        bom_records = tvas_client.get_all_bom_data(vin)
        if not bom_records:
            logger.warning(f"VIN {vin} 没有获取到BOM数据，插入空VIN记录")
            # 修复：如果没有任何记录，强制插入一条空VIN记录
            return [{"vin": ""}]
        
        # 转换为飞书表格格式（未匹配规格的记录VIN已设为空）
        formatted_records = []
        for bom_record in bom_records:
            formatted_record = convert_bom_record_with_mapping(bom_record, vin)
            formatted_records.append(formatted_record)
        
        logger.info(f"VIN {vin} 格式化了 {len(formatted_records)} 条BOM记录")
        return formatted_records
        
    except Exception as e:
        logger.error(f"同步VIN {vin} BOM数据失败: {str(e)}")
        return []

def process_single_vehicle_bom(vin: str, vin_values: dict, tvas_client) -> tuple:
    """
    处理单个车辆的BOM数据同步
    :param vin: 车辆识别码
    :param vin_values: 车辆相关参数
    :param tvas_client: TVAS客户端实例
    :return: (vin, bom_records)
    """
    try:
        # 检查是否需要重新生成
        update_flag = vin_values.get("fields", {}).get("每次重新生成")
        if update_flag == "否":
            logger.info(f"跳过VIN {vin}（设置为不重新生成）")
            return vin, []
        
        # 同步BOM数据
        bom_records = sync_bom_data_for_vehicle(vin, tvas_client)
        return vin, bom_records
        
    except Exception as e:
        logger.error(f"处理车辆 {vin} BOM数据错误: {str(e)}")
        return vin, []


def sync_bom_data_concurrently(vehicle_info_dict: dict, tvas_client, max_workers: int = 12):
    """
    并发同步所有车辆的BOM数据
    :param vehicle_info_dict: 车辆信息字典
    :param tvas_client: TVAS客户端实例
    :param max_workers: 最大并发数
    """
    logger.info(f"并发同步 {len(vehicle_info_dict)} 辆车的BOM数据，最大并发数: {max_workers}")

    # 初始化BOM表格操作对象
    bom_table = VehicleBom()

    # 按需求调整：不再清空历史数据，避免删除人工需要的占位行（VIN 为空）
    # 同时，为保证占位行位于表格最前面，先收集占位行，统一先插入；再插入正常数据
    placeholder_records = []  # VIN为空的占位行（当某VIN无任何匹配时才添加一行）
    normal_records = []       # 正常匹配且格式化后的记录

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_vin = {
            executor.submit(process_single_vehicle_bom, vin, vals, tvas_client): vin
            for vin, vals in vehicle_info_dict.items()
        }

        # 处理完成的任务（仅收集，不立即落库，保证占位行优先插入）
        completed = 0
        total = len(future_to_vin)

        for future in as_completed(future_to_vin):
            completed += 1
            vin = future_to_vin[future]

            try:
                _, bom_records = future.result()
                logger.info(f"BOM同步进度: {completed}/{total} - 完成车辆 {vin}")

                if not bom_records:
                    continue

                # 识别并分类：仅当返回的是“占位行”时（只有 vin 且为空）放入占位集合
                for rec in bom_records:
                    if isinstance(rec, dict) and len(rec.keys()) == 1 and rec.get("vin", None) == "":
                        placeholder_records.append(rec)
                    else:
                        # 过滤真正的“空行”（所有字段都是空字符串或空白）
                        if all((str(v).strip() == "") for v in rec.values()):
                            continue
                        normal_records.append(rec)

            except Exception as e:
                logger.error(f"处理车辆 {vin} BOM数据异常: {str(e)}")

    def batch_insert(records: list, batch_size: int = 500):
        if not records:
            return
        logger.info(f"准备批量插入 {len(records)} 条BOM记录，批大小 {batch_size}")
        for i in range(0, len(records), batch_size):
            batch = records[i:i+batch_size]
            formatted = [{"fields": r} for r in batch]
            if not bom_table.batch_create(formatted):
                logger.error("BOM批量插入失败，尝试逐条插入该批次")
                success_count = 0
                for item in formatted:
                    try:
                        if bom_table.create_record(item.get("fields", item)):
                            success_count += 1
                    except Exception as e:
                        logger.error(f"BOM单条插入失败: {e}")
                logger.info(f"BOM逐条插入完成: {success_count}/{len(formatted)}")

    # 先插入占位行（VIN为空），确保显示在表格最前面
    batch_insert(placeholder_records)
    # 再插入正常数据
    batch_insert(normal_records)

    logger.info("所有车辆BOM数据同步完成")


def main():
    """BOM数据同步主函数"""
    logger.info("开始BOM数据同步程序")
    
    # 初始化机器人通知
    robot = robot_request.RebotRequest()
    
    try:
        # 登录TVAS系统
        def login_system(url, element_id):
            """通用登录函数"""
            login = login_nio.LoginNio(url, element_id)
            return login.get_cookie()
        
        logger.info("登录TVAS系统...")
        tvas_cookie = login_system("https://tvas.nioint.com/tvas/vehicleResource", "container")
        tvas_client = get_tvas_info.GetTvasInfo(tvas_cookie)
        
        # 获取车辆信息
        logger.info("获取车辆信息...")
        summary_table = vehicle_sw_version_table.Summary()
        vehicle_info = summary_table.get_vehicle_info_dict()
        
        logger.info(f"获取到 {len(vehicle_info)} 辆车的信息")

        # 开始同步BOM数据
        start_time = time.time()
        sync_bom_data_concurrently(vehicle_info, tvas_client, max_workers=4)
        end_time = time.time()
        
        # 完成通知
        duration = end_time - start_time
        msg = f"BOM数据同步完成！共处理 {len(vehicle_info)} 辆车，耗时 {duration:.2f} 秒"
        logger.info(msg)
        robot.set_payload("[BOM数据同步]运行正常", msg)
        robot.request()
        
    except Exception as e:
        error_msg = f"BOM数据同步失败: {str(e)}"
        logger.error(error_msg)
        robot.set_payload("[BOM数据同步]运行异常", error_msg)
        robot.request()


if __name__ == "__main__":
    main()
