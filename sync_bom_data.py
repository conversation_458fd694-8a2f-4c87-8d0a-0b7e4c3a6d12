#!/usr/bin/python3
"""
BOM数据同步脚本
独立运行BOM数据同步功能，从TVAS系统获取车辆BOM信息并同步到飞书表格
"""
import re
import datetime
import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import config
import login_nio
import get_tvas_info
import vehicle_sw_version_table
import robot_request
from lark_requests import VehicleBom 
from log_config import logger


def convert_bom_record_with_mapping(tvas_record, vin):
    """
    使用字段映射转换BOM记录格式。
    - en_name 仅保留正则提取的轮胎规格，并规范化为去空格的大写形式（如 245/45R20）
    - VIN 原样保留（不因未匹配到规格而置空）
    :param tvas_record: TVAS原始BOM记录
    :param vin: 车辆识别码
    :return: 格式化后的记录
    """
    tire_pattern = re.compile(r'\d{3}/\d{2}\s*R\d{2}')

    # BOM字段映射（基于飞书表格实际字段）
    field_mapping = {
        "vin": "vin",
        "part_number": "part_number",
        "en_name": "en_name",
        "cn_name": "cn_name",
        "quantity": "quantity",
        "uom": "uom",
        "partition_code": "partition_code",
        "creator": "creator",
        "create_time": "create_time",
        "modifier": "modifier",
        "modify_time": "modify_time",
        "status": "status"
    }

    def format_timestamp(timestamp):
        """格式化时间戳"""
        if not timestamp:
            return ""
        try:
            dt = datetime.datetime.fromtimestamp(int(timestamp))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError) as e:
            logger.warning(f"时间戳转换失败: {timestamp}, 错误: {e}")
            return str(timestamp)

    # 构建格式化记录
    formatted_record = {}

    for tvas_field, feishu_field in field_mapping.items():
        if tvas_field == "vin":
            formatted_record[feishu_field] = vin
        elif tvas_field == "create_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("create_time"))
        elif tvas_field == "modify_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("modify_time"))
        elif tvas_field == "quantity":
            formatted_record[feishu_field] = str(tvas_record.get(tvas_field, ""))
        elif tvas_field == "en_name":
            raw_en = tvas_record.get("en_name", "") or ""
            match = tire_pattern.search(raw_en)
            if match:
                spec = match.group()
                # 规范化：移除空白并大写（245/45R20 样式）
                normalized_spec = re.sub(r"\s+", "", spec).upper()
                formatted_record[feishu_field] = normalized_spec
            else:
                formatted_record[feishu_field] = ""
        else:
            value = tvas_record.get(tvas_field, "")
            formatted_record[feishu_field] = str(value) if value is not None else ""

    return formatted_record


def sync_bom_data_for_vehicle(vin: str, tvas_client) -> list:
    """
    同步单个车辆的BOM数据到飞书表格
    :param vin: 车辆识别码
    :param tvas_client: TVAS客户端实例
    :return: BOM记录列表
    """
    try:
        logger.info(f"开始同步VIN {vin} 的BOM数据")
        
        # 获取BOM数据（已处理去重）
        bom_records = tvas_client.get_all_bom_data(vin)
        if not bom_records:
            logger.warning(f"VIN {vin} 没有获取到BOM数据，插入占位记录（仅包含VIN）")
            # 无任何正则匹配的记录时，返回仅包含 VIN 的占位行
            return [{"vin": vin}]

        # 转换为飞书表格格式（en_name 将被正则规格覆盖）
        formatted_records = []
        for bom_record in bom_records:
            formatted_record = convert_bom_record_with_mapping(bom_record, vin)
            formatted_records.append(formatted_record)
        
        logger.info(f"VIN {vin} 格式化了 {len(formatted_records)} 条BOM记录")
        return formatted_records
        
    except Exception as e:
        logger.error(f"同步VIN {vin} BOM数据失败: {str(e)}")
        return []

def process_single_vehicle_bom(vin: str, vin_values: dict, tvas_client) -> tuple:
    """
    处理单个车辆的BOM数据同步
    :param vin: 车辆识别码
    :param vin_values: 车辆相关参数
    :param tvas_client: TVAS客户端实例
    :return: (vin, bom_records)
    """
    try:
        # 检查是否需要重新生成
        update_flag = vin_values.get("fields", {}).get("每次重新生成")
        if update_flag == "否":
            logger.info(f"跳过VIN {vin}（设置为不重新生成）")
            return vin, []
        
        # 同步BOM数据
        bom_records = sync_bom_data_for_vehicle(vin, tvas_client)
        return vin, bom_records
        
    except Exception as e:
        logger.error(f"处理车辆 {vin} BOM数据错误: {str(e)}")
        return vin, []


def sync_bom_data_concurrently(vehicle_info_dict: dict, tvas_client, max_workers: int = 12, bom_table=None):
    """
    并发同步所有车辆的BOM数据（增量策略）
    规则：
    - VIN 在汇总表存在但本次无匹配：若BOM表已有该 VIN 的记录（占位/人工），跳过不动；若没有，则新增一条仅含 VIN 的占位行
    - VIN 在汇总表存在且本次有匹配：对“规格类记录”做按规格的 upsert（更新/新增）；并删除该 VIN 下已存在但本次不存在的“规格类记录”
    - VIN 不在汇总表（被删除）：删除该 VIN 的所有记录
    """
    logger.info(f"并发同步 {len(vehicle_info_dict)} 辆车的BOM数据，最大并发数: {max_workers}")

    # 使用 vehicle_sw_version_table 的 VehicleBom，便于按 VIN 删除
    bom_table = bom_table or vehicle_sw_version_table.VehicleBom()

    # 读取当前 BOM 表所有记录，构建 {vin: [record]} 映射，记录中包含 record_id 和 fields
    all_records = bom_table.get_all_records()
    vin_to_records = {}
    def _extract_vin(vin_field):
        if isinstance(vin_field, list) and vin_field:
            return vin_field[0].get("text", "")
        if isinstance(vin_field, str):
            return vin_field
        return ""
    for rec in all_records:
        fields = rec.get("fields", {})
        vin = _extract_vin(fields.get("vin"))
        if vin is None:
            vin = ""
        vin_to_records.setdefault(vin, []).append(rec)

    # 计算被删除的 VIN（BOM 表里有，但汇总表里没有）并清理
    summary_vins = set(vehicle_info_dict.keys())
    existing_vins = set(vin for vin in vin_to_records.keys() if vin)
    stale_vins = existing_vins - summary_vins
    if stale_vins:
        logger.info(f"检测到 {len(stale_vins)} 个已被删除的VIN，将清理其BOM数据")
        for vin in stale_vins:
            try:
                vehicle_sw_version_table.VehicleBom().delete_bom_records_by_vin(vin)
            except Exception as e:
                logger.error(f"删除VIN {vin} 的历史BOM数据失败: {e}")

    # 第一步：并发拉取/转换 VIN → 记录列表
    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(process_single_vehicle_bom, vin, vals, tvas_client): vin for vin, vals in vehicle_info_dict.items()}
        for fut in as_completed(futures):
            vin = futures[fut]
            try:
                _, recs = fut.result()
                results[vin] = recs or []
            except Exception as e:
                logger.error(f"VIN {vin} 获取BOM数据失败: {e}")
                results[vin] = []

    # 正则用于识别“规格类记录”
    tire_pattern = re.compile(r"\d{3}/\d{2}\s*R\d{2}")
    def norm_spec(s: str) -> str:
        return re.sub(r"\s+", "", s).upper()

    # 第二步：根据结果执行占位、更新(New/Update)、删除(失配规格)等操作
    placeholders_to_create = []
    records_to_create = []
    records_to_update = []
    records_to_delete = []

    for vin, recs in results.items():
        existing = vin_to_records.get(vin, [])
        # 构建现有“规格类记录”映射：{规范化规格: (record_id, fields)}
        existing_spec_map = {}
        for r in existing:
            fields = r.get("fields", {})
            en = fields.get("en_name", "")
            if isinstance(en, list) and en:
                en = en[0].get("text", "")
            if isinstance(en, str) and tire_pattern.search(en or ""):
                existing_spec_map[norm_spec(en)] = (r.get("record_id"), fields)

        # 过滤真正的空行
        filtered = [rec for rec in recs if not all((str(v).strip() == "") for v in rec.values())]

        if not filtered:
            # 无匹配：若已存在该 VIN 的任意记录，跳过；否则创建占位
            if vin in vin_to_records:
                continue
            placeholders_to_create.append({"vin": vin})
            continue

        # 有匹配：对规格类记录做 upsert（按 en_name 的规格值）
        current_specs = set()
        for rec in filtered:
            spec = rec.get("en_name", "")
            if spec:
                key = norm_spec(spec)
                current_specs.add(key)
                if key in existing_spec_map:
                    record_id, _ = existing_spec_map[key]
                    records_to_update.append({"record_id": record_id, "fields": rec})
                else:
                    records_to_create.append(rec)
        # 删除现有但本次不存在的规格类记录
        for key, (record_id, _) in existing_spec_map.items():
            if key not in current_specs and record_id:
                records_to_delete.append(record_id)

    # 先插入占位行，避免顺序受影响
    if placeholders_to_create:
        logger.info(f"新增占位行 {len(placeholders_to_create)} 条")
        bom_table.batch_create([{"fields": r} for r in placeholders_to_create])

    # 创建新增的规格记录
    if records_to_create:
        logger.info(f"新增规格记录 {len(records_to_create)} 条")
        bom_table.batch_create([{"fields": r} for r in records_to_create])

    # 更新已有的规格记录
    if records_to_update:
        logger.info(f"更新规格记录 {len(records_to_update)} 条")
        bom_table.batch_update(records_to_update)

    # 删除失配的老旧规格记录
    if records_to_delete:
        logger.info(f"删除失配规格记录 {len(records_to_delete)} 条")
        bom_table.batch_delete(records_to_delete)

    logger.info("所有车辆BOM数据同步完成")


def main():
    """BOM数据同步主函数"""
    logger.info("开始BOM数据同步程序")
    
    # 初始化机器人通知
    robot = robot_request.RebotRequest()
    
    try:
        # 登录TVAS系统
        def login_system(url, element_id):
            """通用登录函数"""
            login = login_nio.LoginNio(url, element_id)
            return login.get_cookie()
        
        logger.info("登录TVAS系统...")
        tvas_cookie = login_system("https://tvas.nioint.com/tvas/vehicleResource", "container")
        tvas_client = get_tvas_info.GetTvasInfo(tvas_cookie)
        
        # 获取车辆信息
        logger.info("获取车辆信息...")
        summary_table = vehicle_sw_version_table.Summary()
        vehicle_info = summary_table.get_vehicle_info_dict()
        
        logger.info(f"获取到 {len(vehicle_info)} 辆车的信息")

        # 开始同步BOM数据
        start_time = time.time()
        sync_bom_data_concurrently(vehicle_info, tvas_client, max_workers=4)
        end_time = time.time()
        
        # 完成通知
        duration = end_time - start_time
        msg = f"BOM数据同步完成！共处理 {len(vehicle_info)} 辆车，耗时 {duration:.2f} 秒"
        logger.info(msg)
        robot.set_payload("[BOM数据同步]运行正常", msg)
        robot.request()
        
    except Exception as e:
        error_msg = f"BOM数据同步失败: {str(e)}"
        logger.error(error_msg)
        robot.set_payload("[BOM数据同步]运行异常", error_msg)
        robot.request()


if __name__ == "__main__":
    main()
