#!/usr/bin/python3
"""
BOM数据同步脚本
独立运行BOM数据同步功能，从TVAS系统获取车辆BOM信息并同步到飞书表格
"""

import datetime
import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import config
import login_nio
import get_tvas_info
import vehicle_sw_version_table
import robot_request
from log_config import logger


def sync_bom_data_for_vehicle(vin: str, tvas_client) -> list:
    """
    同步单个车辆的BOM数据到飞书表格
    :param vin: 车辆识别码
    :param tvas_client: TVAS客户端实例
    :return: BOM记录列表
    """
    try:
        logger.info(f"开始同步VIN {vin} 的BOM数据")
        
        # 获取BOM数据
        bom_records = tvas_client.get_all_bom_data(vin)
        if not bom_records:
            logger.warning(f"VIN {vin} 没有获取到BOM数据")
            return []
        
        # 转换为飞书表格格式
        formatted_records = []
        for bom_record in bom_records:
            # 提取时间戳并转换为毫秒
            create_time = bom_record.get("create_time")
            modify_time = bom_record.get("modify_time")
            create_time_ms = int(create_time) * 1000 if create_time else None
            modify_time_ms = int(modify_time) * 1000 if modify_time else None
            
            formatted_record = {
                "vin": vin,
                "part_number": bom_record.get("part_number", ""),
                "part_revision": bom_record.get("part_revision", ""),
                "en_name": bom_record.get("en_name", ""),
                "cn_name": bom_record.get("cn_name", ""),
                "quantity": bom_record.get("quantity", ""),
                "uom": bom_record.get("uom", ""),
                "partition_code": bom_record.get("partition_code", ""),
                "creator": bom_record.get("creator", ""),
                "create_time": create_time_ms,
                "modifier": bom_record.get("modifier", ""),
                "modify_time": modify_time_ms,
                "status": bom_record.get("status", "")
            }
            formatted_records.append(formatted_record)
        
        logger.info(f"VIN {vin} 格式化了 {len(formatted_records)} 条BOM记录")
        return formatted_records
        
    except Exception as e:
        logger.error(f"同步VIN {vin} BOM数据失败: {str(e)}")
        return []


def process_single_vehicle_bom(vin: str, vin_values: dict, tvas_client) -> tuple:
    """
    处理单个车辆的BOM数据同步
    :param vin: 车辆识别码
    :param vin_values: 车辆相关参数
    :param tvas_client: TVAS客户端实例
    :return: (vin, bom_records)
    """
    try:
        # 检查是否需要重新生成
        update_flag = vin_values.get("fields", {}).get("每次重新生成")
        if update_flag == "否":
            logger.info(f"跳过VIN {vin}（设置为不重新生成）")
            return vin, []
        
        # 同步BOM数据
        bom_records = sync_bom_data_for_vehicle(vin, tvas_client)
        return vin, bom_records
        
    except Exception as e:
        logger.error(f"处理车辆 {vin} BOM数据错误: {str(e)}")
        return vin, []


def sync_bom_data_concurrently(vehicle_info_dict: dict, tvas_client, max_workers: int = 4):
    """
    并发同步所有车辆的BOM数据
    :param vehicle_info_dict: 车辆信息字典
    :param tvas_client: TVAS客户端实例
    :param max_workers: 最大并发数
    """
    logger.info(f"并发同步 {len(vehicle_info_dict)} 辆车的BOM数据，最大并发数: {max_workers}")
    
    # 初始化BOM表格
    bom_table = vehicle_sw_version_table.VehicleBom()
    
    # 获取已存在的VIN，避免重复同步
    existing_vins = bom_table.get_existing_bom_vins()
    logger.info(f"BOM表中已存在 {len(existing_vins)} 个VIN的数据")
    
    all_bom_records = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_vin = {
            executor.submit(process_single_vehicle_bom, vin, vals, tvas_client): vin
            for vin, vals in vehicle_info_dict.items()
        }
        
        # 处理完成的任务
        completed = 0
        total = len(future_to_vin)
        
        for future in as_completed(future_to_vin):
            completed += 1
            vin = future_to_vin[future]
            
            try:
                _, bom_records = future.result()
                logger.info(f"BOM同步进度: {completed}/{total} - 完成车辆 {vin}")
                
                if bom_records:
                    # 如果VIN已存在，先删除旧数据
                    if vin in existing_vins:
                        logger.info(f"删除VIN {vin} 的旧BOM数据")
                        bom_table.delete_bom_records_by_vin(vin)
                    
                    all_bom_records.extend(bom_records)
                
                # 每处理完一定数量的车辆，批量插入一次
                if len(all_bom_records) >= 1000:  # 每1000条记录批量插入一次
                    logger.info(f"批量插入 {len(all_bom_records)} 条BOM记录")
                    formatted_records = [{"fields": record} for record in all_bom_records]
                    
                    if not bom_table.batch_create(formatted_records):
                        logger.error("BOM批量插入失败，尝试逐条插入")
                        success_count = 0
                        for record in formatted_records:
                            try:
                                if bom_table.create_record(record.get("fields", record)):
                                    success_count += 1
                            except Exception as e:
                                logger.error(f"BOM单条插入失败: {e}")
                        logger.info(f"BOM逐条插入完成: {success_count}/{len(formatted_records)}")
                    
                    all_bom_records = []  # 清空已处理的记录
                
            except Exception as e:
                logger.error(f"处理车辆 {vin} BOM数据异常: {str(e)}")
    
    # 处理剩余的记录
    if all_bom_records:
        logger.info(f"插入剩余的 {len(all_bom_records)} 条BOM记录")
        formatted_records = [{"fields": record} for record in all_bom_records]
        
        if not bom_table.batch_create(formatted_records):
            logger.error("BOM最终批量插入失败，尝试逐条插入")
            success_count = 0
            for record in formatted_records:
                try:
                    if bom_table.create_record(record.get("fields", record)):
                        success_count += 1
                except Exception as e:
                    logger.error(f"BOM单条插入失败: {e}")
            logger.info(f"BOM最终逐条插入完成: {success_count}/{len(formatted_records)}")
    
    logger.info("所有车辆BOM数据同步完成")


def main():
    """BOM数据同步主函数"""
    logger.info("开始BOM数据同步程序")
    
    # 初始化机器人通知
    robot = robot_request.RebotRequest()
    
    try:
        # 登录TVAS系统
        def login_system(url, element_id):
            """通用登录函数"""
            login = login_nio.LoginNio(url, element_id)
            return login.get_cookie()
        
        logger.info("登录TVAS系统...")
        tvas_cookie = login_system("https://tvas.nioint.com/tvas/vehicleResource", "container")
        tvas_client = get_tvas_info.GetTvasInfo(tvas_cookie)
        
        # 获取车辆信息
        logger.info("获取车辆信息...")
        summary_table = vehicle_sw_version_table.Summary()
        vehicle_info = summary_table.get_vehicle_info_dict()
        
        logger.info(f"获取到 {len(vehicle_info)} 辆车的信息")
        
        # 开始同步BOM数据
        start_time = time.time()
        sync_bom_data_concurrently(vehicle_info, tvas_client, max_workers=4)
        end_time = time.time()
        
        # 完成通知
        duration = end_time - start_time
        msg = f"BOM数据同步完成！共处理 {len(vehicle_info)} 辆车，耗时 {duration:.2f} 秒"
        logger.info(msg)
        robot.set_payload("[BOM数据同步]运行正常", msg)
        robot.request()
        
    except Exception as e:
        error_msg = f"BOM数据同步失败: {str(e)}"
        logger.error(error_msg)
        robot.set_payload("[BOM数据同步]运行异常", error_msg)
        robot.request()


if __name__ == "__main__":
    main()
