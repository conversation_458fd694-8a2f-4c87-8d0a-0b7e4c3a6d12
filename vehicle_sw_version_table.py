import datetime

import config
import lark_requests  # 导入飞书表格操作基础类
from log_config import logger  # 导入日志工具


def from_timestamp_to_str(timestamp):
    """
    将时间戳转换为可读性强的日期时间字符串
    
    参数:
        timestamp: 时间戳（秒级或毫秒级）
        
    返回:
        格式化的日期时间字符串，格式为"%Y-%m-%d %H:%M"；
        如果输入为None，则返回"--"
    """
    if timestamp is None:
        return "--"
    # 处理可能的毫秒级时间戳，取前10位转为整数（秒级）
    datetime_struct = datetime.datetime.fromtimestamp(int(str(timestamp)[0:10]))
    # 格式化为指定字符串
    return datetime.datetime.strftime(datetime_struct, "%Y-%m-%d %H:%M")


class Summary(lark_requests.LarkRequests):
    """
    车辆汇总信息处理类，继承自LarkRequests，用于处理飞书汇总表格数据
    """
    
    def __init__(self):
        """初始化汇总信息处理对象，关联到配置文件中的汇总表格"""
        # 调用父类构造方法，传入应用令牌和表格ID（从配置文件获取）
        super().__init__(config.APP_TOKEN, config.TABLE_ID)

    def get_vehicle_info_dict(self) -> dict:
        """
        从汇总表格中提取车辆信息，构建以VIN为键的字典
        
        返回:
            字典结构，键为VIN（车辆识别码），值包含车辆相关信息和记录ID
        """
        vehicle_info_dict = {}  # 存储车辆信息的字典，VIN为键
        
        # 遍历汇总表格中的所有记录
        for record in self.get_all_records():
            # 获取"每次重新生成"字段，判断是否需要跳过该记录
            update_flag = record.get("fields", {}).get("每次重新生成")
            if update_flag and update_flag == "否":
                continue  # 跳过不需要重新生成的记录

            # 获取VIN字段（飞书表格中可能为多值字段，存储为列表）
            vin_list = record.get("fields", {}).get("VIN")
            # 检查VIN是否有效
            if vin_list is None or len(vin_list) == 0:
                logger.warning(f"记录缺少VIN信息: {record}")
                continue

            # 提取VIN文本（飞书多值字段格式为[{"text": "值"}]）
            vin = vin_list[0].get("text")
            # 获取当前记录的ID
            record_id = record.get("record_id")
            
            # 仅处理有有效VIN和记录ID的记录
            if vin_list and record_id:
                # 处理重复VIN：如果VIN已存在于字典中，删除当前重复记录
                if vin in vehicle_info_dict.keys():
                    logger.info(f"发现重复VIN {vin}，删除重复记录 {record_id}")
                    self.delete_record(record_id)
                else:
                    # 构建车辆信息字典并添加到结果中
                    tmp_dict = {
                        "fields": {},  # 存储字段信息
                        "record_id": record_id,  # 记录ID
                        # 获取"使用本地DID的软硬件零件号"字段
                        "use_did": record.get("fields", {}).get("使用本地DID的软硬件零件号"),
                        # 获取"车型"字段
                        "vehicle_model": record.get("fields", {}).get("车型"),
                    }
                    vehicle_info_dict[vin] = tmp_dict
            else:
                logger.warning(f"记录信息不完整: {record}")
                
        return vehicle_info_dict


class VehicleDetail(lark_requests.LarkRequests):
    """
    车辆详细信息处理类，继承自LarkRequests，用于处理飞书详细信息表格
    """
    
    def __init__(self):
        """初始化详细信息处理对象，关联到配置文件中的详细表格"""
        super().__init__(config.APP_TOKEN, config.DETAIL_TABLE_ID)

    def get_all_record_ids(self, skip_leo_flag=True) -> list:
        """
        获取详细表格中所有符合条件的记录ID列表
        
        参数:
            skip_leo_flag: 是否跳过车型为"leo"的记录，默认为True
            
        返回:
            符合条件的记录ID列表
        """
        all_record_ids = []  # 存储记录ID的列表
        
        # 遍历详细表格中的所有记录
        for record in self.get_all_records():
            # 获取"每次重新生成"字段，判断是否需要跳过该记录
            update_flag = record.get("fields", {}).get("每次重新生成")
            # 飞书单选字段格式可能为{"value": ["否"]}，检查是否需要跳过
            if update_flag and update_flag.get("value", [""])[0] == "否":
                continue  # 跳过不需要重新生成的记录
            
            # 如果需要跳过leo车型，且当前记录车型为leo，则跳过
            if skip_leo_flag and str(record.get("fields", {}).get("车型", "")).lower() == "leo":
                logger.info(f"跳过leo车型记录: {record}")
                continue

            # 获取记录ID
            record_id = record.get("record_id")
            if record_id:
                all_record_ids.append(record_id)
            else:
                logger.warning(f"记录缺少ID: {record}")
                
        return all_record_ids


class RawEcuData(lark_requests.LarkRequests):
    """
    原始ECU数据处理类，继承自LarkRequests，用于处理飞书原始ECU数据表
    """
    
    def __init__(self):
        """初始化原始ECU数据处理对象，关联到配置文件中的原始ECU表格"""
        super().__init__(config.APP_TOKEN, config.RAW_ECU_TABLE_ID)

    def get_all_record_ids(self) -> list:
        """
        获取原始ECU数据表中所有记录的ID列表
        
        返回:
            所有记录的ID列表
        """
        all_record_ids = []  # 存储记录ID的列表
        
        # 遍历原始ECU表格中的所有记录
        for record in self.get_all_records():
            # 获取记录ID
            record_id = record.get("record_id")
            if record_id:
                all_record_ids.append(record_id)
            else:
                logger.warning(f"原始ECU记录缺少ID: {record}")

        return all_record_ids


class VehicleBom(lark_requests.LarkRequests):
    """
    车辆BOM（物料清单）数据处理类，继承自LarkRequests，用于处理飞书BOM数据表
    """

    def __init__(self):
        """初始化BOM数据处理对象，关联到配置文件中的BOM表格"""
        super().__init__(config.APP_TOKEN, config.BOM_TABLE_ID)

    def get_all_record_ids(self) -> list:
        """
        获取BOM数据表中所有记录的ID列表

        返回:
            所有记录的ID列表
        """
        all_record_ids = []  # 存储记录ID的列表

        # 遍历BOM表格中的所有记录
        for record in self.get_all_records():
            # 获取记录ID
            record_id = record.get("record_id")
            if record_id:
                all_record_ids.append(record_id)
            else:
                logger.warning(f"BOM记录缺少ID: {record}")

        return all_record_ids

    def get_existing_bom_vins(self) -> set:
        """
        获取BOM表中已存在的所有VIN

        返回:
            包含所有已存在VIN的集合
        """
        existing_vins = set()

        for record in self.get_all_records():
            vin_field = record.get("fields", {}).get("vin")
            if vin_field:
                # 处理飞书文本字段格式
                if isinstance(vin_field, list) and len(vin_field) > 0:
                    vin = vin_field[0].get("text", "")
                elif isinstance(vin_field, str):
                    vin = vin_field
                else:
                    continue

                if vin:
                    existing_vins.add(vin)

        return existing_vins

    def delete_bom_records_by_vin(self, vin: str) -> bool:
        """
        删除指定VIN的所有BOM记录

        参数:
            vin: 车辆识别码

        返回:
            布尔值：True表示删除成功，False表示失败
        """
        records_to_delete = []

        for record in self.get_all_records():
            vin_field = record.get("fields", {}).get("vin")
            record_vin = ""

            if isinstance(vin_field, list) and len(vin_field) > 0:
                record_vin = vin_field[0].get("text", "")
            elif isinstance(vin_field, str):
                record_vin = vin_field

            if record_vin == vin:
                record_id = record.get("record_id")
                if record_id:
                    records_to_delete.append(record_id)

        if records_to_delete:
            logger.info(f"删除VIN {vin} 的 {len(records_to_delete)} 条BOM记录")
            return self.batch_delete(records_to_delete)
        else:
            logger.info(f"VIN {vin} 没有找到需要删除的BOM记录")
            return True
    