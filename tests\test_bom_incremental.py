import unittest
from unittest.mock import MagicMock
import re

import sync_bom_data

class DummyBomTable:
    def __init__(self, existing):
        # existing: list of {record_id, fields:{vin,en_name,...}}
        self._records = list(existing)
        self.created = []
        self.updated = []
        self.deleted = []
        self.operations = []  # 记录操作历史
    def get_all_records(self):
        return list(self._records)
    def batch_create(self, records):
        # records: [{"fields": {...}}]
        for r in records:
            fields = r.get("fields", {})
            self.created.append(fields)
            record_id = f"new_{len(self._records)+1}"
            self._records.append({"record_id": record_id, "fields": fields})
            self.operations.append(("create", record_id, fields))
        return True
    def batch_update(self, records):
        # records: [{"record_id", "fields"}]
        self.updated.extend(records)
        for r in records:
            self.operations.append(("update", r.get("record_id"), r.get("fields")))
        return True
    def batch_delete(self, ids):
        self.deleted.extend(ids)
        for rid in ids:
            self.operations.append(("delete", rid, None))
            # 从记录中移除
            self._records = [r for r in self._records if r.get("record_id") != rid]
        return True

class FakeTvasClient:
    def __init__(self, data_by_vin):
        self.data_by_vin = data_by_vin
    def get_all_bom_data(self, vin):
        return self.data_by_vin.get(vin, [])

def mk_rec(vin, spec):
    # en_name 已经过 convert 成规格字符串
    return {"vin": vin, "en_name": spec, "sort_index": 1}

class TestIncrementalSync(unittest.TestCase):
    def test_skip_when_placeholder_exists(self):
        # 飞书已有 VIN 的占位行（只有 vin）
        existing = [{"record_id": "r1", "fields": {"vin": "VIN_A"}}]
        bom_table = DummyBomTable(existing)
        tvas = FakeTvasClient({"VIN_A": []})  # 本次无匹配
        vehicle_info = {"VIN_A": {"fields": {}}}

        sync_bom_data.sync_bom_data_concurrently(vehicle_info, tvas, max_workers=1, bom_table=bom_table)
        # 不创建新占位，不删除、不更新
        self.assertEqual(bom_table.created, [])
        self.assertEqual(bom_table.updated, [])
        self.assertEqual(bom_table.deleted, [])

    def test_upsert_and_delete_specs(self):
        # 现有：VIN_B 有一个 245/45R20，另一个 245/50R20 不存在
        existing = [{"record_id": "r2", "fields": mk_rec("VIN_B", "245/45R20")}]
        bom_table = DummyBomTable(existing)
        # 本次：返回两个规格，且 245/45R20 需要更新（这里看作更新触发），新增 245/50R20，同时删除现有中不存在的规格无（留空）
        tvas = FakeTvasClient({"VIN_B": [mk_rec("VIN_B", "245/45R20"), mk_rec("VIN_B", "245/50R20")]})
        vehicle_info = {"VIN_B": {"fields": {}}}

        sync_bom_data.sync_bom_data_concurrently(vehicle_info, tvas, max_workers=1, bom_table=bom_table)
        # 应新增 245/50R20
        self.assertTrue(any(r.get("en_name") == "245/50R20" for r in bom_table.created))
        # 应更新已有的 245/45R20
        self.assertTrue(any(u.get("fields", {}).get("en_name") == "245/45R20" for u in bom_table.updated))

    def test_delete_vin_no_longer_in_summary(self):
        # 表里有 VIN_C；汇总里没有 VIN_C，应该删除
        existing = [{"record_id": "r3", "fields": mk_rec("VIN_C", "245/45R20")}]
        bom_table = DummyBomTable(existing)
        tvas = FakeTvasClient({})
        vehicle_info = {"VIN_D": {"fields": {}}}  # 汇总里不含 VIN_C

        sync_bom_data.sync_bom_data_concurrently(vehicle_info, tvas, max_workers=1, bom_table=bom_table)
        # VIN_C 的规格记录应该被删除
        self.assertTrue(any(op[0] == "delete" and op[1] == "r3" for op in bom_table.operations))
        # VIN_D 无匹配，应该创建占位行
        self.assertEqual(len(bom_table.created), 1)
        self.assertEqual(bom_table.created[0]["vin"], "VIN_D")
        self.assertEqual(bom_table.created[0]["sort_index"], 0)
        # 没有更新操作
        self.assertEqual(bom_table.updated, [])

if __name__ == '__main__':
    unittest.main()

