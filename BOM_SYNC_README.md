# BOM数据同步功能说明

## 功能概述

本功能实现了从TVAS系统获取车辆BOM（物料清单）信息，并自动同步到飞书表格的完整流程。

## 数据流程图

```
飞书汇总表 → 获取VIN列表 → TVAS系统查询BOM → 数据格式转换 → 飞书BOM表
    ↓              ↓              ↓              ↓              ↓
车辆信息      VIN码列表      原始BOM数据      标准化格式      最终存储
```

## 核心功能模块

### 1. TVAS接口扩展 (get_tvas_info.py)

新增了两个关键方法：

#### `query_bom_by_vin(vin, page_size=50, page_count=1)`
- **功能**: 获取单页BOM数据
- **参数**: 
  - `vin`: 车辆识别码
  - `page_size`: 每页记录数（默认50）
  - `page_count`: 页码（从1开始）
- **返回**: BOM数据字典，包含`total_count`和`list`字段

#### `get_all_bom_data(vin)`
- **功能**: 获取指定VIN的所有BOM数据（自动处理分页）
- **参数**: `vin`: 车辆识别码
- **返回**: 所有BOM记录的列表

**示例调用**:
```python
tvas_client = get_tvas_info.GetTvasInfo(cookie)
all_bom = tvas_client.get_all_bom_data("HJNAAJNC8SA050133")
# 返回: [{"part_number": "P0373183", "part_revision": "AC", ...}, ...]
```

### 2. 飞书表格操作扩展 (vehicle_sw_version_table.py)

新增了`VehicleBom`类，提供BOM表格的完整操作：

#### 主要方法：
- `get_all_record_ids()`: 获取所有记录ID
- `get_existing_bom_vins()`: 获取已存在的VIN集合
- `delete_bom_records_by_vin(vin)`: 删除指定VIN的所有BOM记录

### 3. 主程序集成 (main.py)

新增了BOM数据同步相关函数：

#### `sync_bom_data_for_vehicle(vin)`
- **功能**: 同步单个车辆的BOM数据
- **处理**: 数据获取 → 格式转换 → 时间戳处理

#### `sync_all_bom_data(vehicle_info_dict)`
- **功能**: 批量同步所有车辆的BOM数据
- **特性**: 
  - 智能去重（删除旧数据）
  - 批量插入优化
  - 错误恢复机制

## 数据字段映射

### TVAS原始数据格式:
```json
{
    "id": 4093696,
    "vin": "HJNAAJNC8SA050133",
    "part_number": "P0373183",
    "part_revision": "AC",
    "en_name": "ANTENNA-GNSS MAIN",
    "cn_name": "GNSS 主天线",
    "quantity": "1",
    "uom": "EA",
    "partition_code": "ELE.10.04.05",
    "creator": "VINBOM",
    "create_time": 1754543582,
    "modifier": "VINBOM",
    "modify_time": 1754543582,
    "status": "Assembled"
}
```

### 飞书表格格式:
```python
{
    "vin": "HJNAAJNC8SA050133",
    "part_number": "P0373183",
    "part_revision": "AC",
    "en_name": "ANTENNA-GNSS MAIN",
    "cn_name": "GNSS 主天线",
    "quantity": "1",
    "uom": "EA",
    "partition_code": "ELE.10.04.05",
    "creator": "VINBOM",
    "create_time": 1754543582000,  # 转换为毫秒时间戳
    "modifier": "VINBOM",
    "modify_time": 1754543582000,  # 转换为毫秒时间戳
    "status": "Assembled"
}
```

## 使用方法

### 方法1: 集成到主程序
BOM同步已集成到`main.py`的`ecu_version_checker()`函数中，会在ECU版本检查之前自动执行。

### 方法2: 独立运行BOM同步
```bash
python sync_bom_data.py
```

### 方法3: 测试单个VIN
```bash
python test_bom_sync.py
```

## 性能优化

### 1. 并发处理
- 默认使用4个并发线程处理BOM数据获取
- 可根据系统性能调整`max_workers`参数

### 2. 批量操作
- 每1000条记录批量插入一次，减少API调用次数
- 插入失败时自动降级为逐条插入

### 3. 智能去重
- 检查已存在的VIN，避免重复同步
- 支持增量更新和全量更新

## 错误处理

### 1. 网络错误
- 自动重试机制
- 详细的错误日志记录

### 2. 数据错误
- 字段验证和清理
- 时间戳格式转换

### 3. 飞书API错误
- 批量操作失败时降级为逐条操作
- Token过期自动刷新

## 监控和日志

### 日志级别
- `INFO`: 处理进度和关键状态
- `WARNING`: 数据缺失或异常情况
- `ERROR`: 严重错误和失败情况
- `DEBUG`: 详细的调试信息

### 关键监控指标
- 处理车辆数量
- BOM记录总数
- 同步成功率
- 处理耗时
- 错误统计

## 配置说明

### config.py 新增配置
```python
# BOM物料清单表
BOM_TABLE_ID = "tblvLdUKy6MRZXPX"
```

### 飞书表格URL
https://nio.feishu.cn/base/XuG3bU8uDaR4Nts7geLcTO1Jnhh?table=tblvLdUKy6MRZXPX&view=vewI62gWSb

## 注意事项

1. **数据量**: 每辆车可能有数百到上千条BOM记录，请确保有足够的存储空间
2. **API限制**: 注意飞书API的调用频率限制，批量操作可以有效减少API调用
3. **时间戳**: TVAS返回的是秒级时间戳，需要转换为毫秒级以匹配飞书格式
4. **并发控制**: BOM数据获取是I/O密集型操作，适当的并发可以提升效率
5. **错误恢复**: 单个车辆的BOM获取失败不会影响其他车辆的处理

## 扩展功能

可以根据需要添加以下功能：
- BOM数据变更检测
- 增量同步（只同步变更的记录）
- BOM数据分析和统计
- 自定义过滤条件（如特定分区代码的零件）
