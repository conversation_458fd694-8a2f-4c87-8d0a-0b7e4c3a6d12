#!/usr/bin/python3
"""
最终的BOM数据同步测试
使用正确的字段映射进行完整测试
"""

import datetime
import json
import config
import login_nio
import get_tvas_info
import vehicle_sw_version_table
from log_config import logger


def convert_bom_record_with_mapping(tvas_record, vin):
    """
    使用字段映射转换BOM记录格式
    :param tvas_record: TVAS原始BOM记录
    :param vin: 车辆识别码
    :return: 格式化后的记录
    """
    # BOM字段映射（基于飞书表格实际字段）
    field_mapping = {
        "vin": "vin",
        "part_number": "part_number",
        "en_name": "en_name", 
        "cn_name": "cn_name",
        "quantity": "quantity",
        "uom": "uom",
        "partition_code": "partition_code",
        "creator": "creator",
        "create_time": "create_time",
        "modifier": "modifier",
        "modify_time": "modify_time",
        "status": "status"
    }
    
    def format_timestamp(timestamp):
        """格式化时间戳"""
        if not timestamp:
            return ""
        try:
            dt = datetime.datetime.fromtimestamp(int(timestamp))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError) as e:
            logger.warning(f"时间戳转换失败: {timestamp}, 错误: {e}")
            return str(timestamp)
    
    # 构建格式化记录
    formatted_record = {}
    
    for tvas_field, feishu_field in field_mapping.items():
        if tvas_field == "vin":
            formatted_record[feishu_field] = vin
        elif tvas_field == "create_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("create_time"))
        elif tvas_field == "modify_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("modify_time"))
        elif tvas_field == "quantity":
            # 确保数量为字符串
            formatted_record[feishu_field] = str(tvas_record.get(tvas_field, ""))
        else:
            value = tvas_record.get(tvas_field, "")
            formatted_record[feishu_field] = str(value) if value is not None else ""
    
    return formatted_record


def test_complete_bom_sync(test_vin="HJNAAJNC8SA050133", max_records=20):
    """
    完整的BOM同步测试
    :param test_vin: 测试VIN
    :param max_records: 最大测试记录数
    """
    print("=" * 80)
    print("完整BOM数据同步测试")
    print("=" * 80)
    
    try:
        # 1. 登录TVAS系统
        print("🔐 登录TVAS系统...")
        def login_system(url, element_id):
            login = login_nio.LoginNio(url, element_id)
            return login.get_cookie()
        
        tvas_cookie = login_system("https://tvas.nioint.com/tvas/vehicleResource", "container")
        tvas_client = get_tvas_info.GetTvasInfo(tvas_cookie)
        print("✅ TVAS系统登录成功")
        
        # 2. 获取BOM数据
        print(f"\n📋 获取VIN {test_vin} 的BOM数据...")
        bom_data = tvas_client.query_bom_by_vin(test_vin, page_size=max_records, page_count=1)
        
        if not bom_data or not bom_data.get("list"):
            print(f"❌ 无法获取VIN {test_vin} 的BOM数据")
            return False
        
        records = bom_data["list"]
        total_count = bom_data.get("total_count", 0)
        print(f"✅ 获取到 {len(records)} 条BOM记录（总共 {total_count} 条）")
        
        # 3. 显示原始数据示例
        if records:
            print(f"\n📝 原始TVAS数据示例:")
            sample_record = records[0]
            print(json.dumps(sample_record, ensure_ascii=False, indent=2))
        
        # 4. 转换数据格式
        print(f"\n🔄 转换数据格式...")
        formatted_records = []
        for bom_record in records:
            formatted_record = convert_bom_record_with_mapping(bom_record, test_vin)
            formatted_records.append(formatted_record)
        
        print(f"✅ 成功转换 {len(formatted_records)} 条记录")
        
        # 5. 显示转换后的数据示例
        if formatted_records:
            print(f"\n📋 转换后的数据示例:")
            print(json.dumps(formatted_records[0], ensure_ascii=False, indent=2))
        
        # 6. 初始化飞书表格
        print(f"\n📊 初始化飞书BOM表格...")
        bom_table = vehicle_sw_version_table.VehicleBom()
        
        # 7. 清理测试VIN的旧数据
        print(f"🧹 清理VIN {test_vin} 的旧数据...")
        bom_table.delete_bom_records_by_vin(test_vin)
        
        # 8. 插入新数据
        print(f"📤 插入 {len(formatted_records)} 条新记录...")
        formatted_for_insert = [{"fields": record} for record in formatted_records]
        
        import time
        start_time = time.time()
        
        # 尝试批量插入
        success = bom_table.batch_create(formatted_for_insert)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if success:
            print(f"✅ 批量插入成功！耗时: {duration:.2f} 秒")
        else:
            print("❌ 批量插入失败，尝试逐条插入...")
            success_count = 0
            
            for i, record in enumerate(formatted_for_insert):
                try:
                    if bom_table.create_record(record.get("fields")):
                        success_count += 1
                        print(f"✅ 记录 {i+1}/{len(formatted_for_insert)} 插入成功")
                    else:
                        print(f"❌ 记录 {i+1}/{len(formatted_for_insert)} 插入失败")
                except Exception as e:
                    print(f"❌ 记录 {i+1}/{len(formatted_for_insert)} 插入异常: {str(e)}")
            
            print(f"📊 逐条插入完成: {success_count}/{len(formatted_for_insert)} 条成功")
            success = success_count > 0
        
        # 9. 验证结果
        print(f"\n🔍 验证同步结果...")
        existing_vins = bom_table.get_existing_bom_vins()
        
        if test_vin in existing_vins:
            print(f"✅ VIN {test_vin} 已成功同步到BOM表")
        else:
            print(f"❌ VIN {test_vin} 同步验证失败")
        
        print(f"📈 BOM表现在包含 {len(existing_vins)} 个VIN的数据")
        
        return success
        
    except Exception as e:
        print(f"❌ 完整BOM同步测试失败: {str(e)}")
        logger.error(f"完整BOM同步测试失败: {str(e)}")
        return False


def test_field_mapping_accuracy():
    """测试字段映射的准确性"""
    
    print("\n" + "=" * 80)
    print("字段映射准确性测试")
    print("=" * 80)
    
    # 模拟TVAS数据
    sample_tvas_data = {
        "id": 4093440,
        "vin": "HJNAAJNC8SA050133",
        "part_number": "S0000979",
        "part_revision": "AA",  # 注意：这个字段在飞书表中不存在
        "en_name": "Bolt, M10x1.5x25",
        "cn_name": "六角法兰面螺栓",
        "quantity": "4",
        "uom": "PC",
        "partition_code": "STD.00.00.00",
        "creator": "VINBOM",
        "create_time": 1754543582,
        "modifier": "VINBOM",
        "modify_time": 1754543582,
        "status": "Assembled"
    }
    
    print("📋 TVAS原始数据:")
    print(json.dumps(sample_tvas_data, ensure_ascii=False, indent=2))
    
    # 转换数据
    formatted_data = convert_bom_record_with_mapping(sample_tvas_data, "TEST_VIN")
    
    print("\n📝 转换后的数据:")
    print(json.dumps(formatted_data, ensure_ascii=False, indent=2))
    
    # 检查字段映射
    expected_fields = [
        "vin", "part_number", "en_name", "cn_name", "quantity", "uom",
        "partition_code", "creator", "create_time", "modifier", "modify_time", "status"
    ]
    
    print(f"\n🔍 字段映射检查:")
    for field in expected_fields:
        if field in formatted_data:
            print(f"✅ {field:15} → {formatted_data[field]}")
        else:
            print(f"❌ {field:15} → 缺失")
    
    # 检查时间格式
    create_time = formatted_data.get("create_time", "")
    modify_time = formatted_data.get("modify_time", "")
    
    print(f"\n⏰ 时间格式检查:")
    print(f"创建时间: {create_time} ({'✅ 格式正确' if len(create_time) == 19 else '❌ 格式错误'})")
    print(f"修改时间: {modify_time} ({'✅ 格式正确' if len(modify_time) == 19 else '❌ 格式错误'})")


def main():
    """主函数"""
    
    print("最终BOM数据同步测试")
    print("=" * 80)
    
    # 测试字段映射
    test_field_mapping_accuracy()
    
    # 询问是否进行完整测试
    print("\n" + "=" * 80)
    user_input = input("是否进行完整的BOM同步测试？(y/n, 默认n): ").strip().lower()
    
    if user_input in ['y', 'yes']:
        test_vin = input("请输入测试VIN (默认: HJNAAJNC8SA050133): ").strip()
        if not test_vin:
            test_vin = "HJNAAJNC8SA050133"
        
        max_records = input("最大测试记录数 (默认: 20): ").strip()
        try:
            max_records = int(max_records) if max_records else 20
        except:
            max_records = 20
        
        success = test_complete_bom_sync(test_vin, max_records)
        
        if success:
            print("\n🎉 BOM同步测试成功！")
            print("现在可以在主程序中使用BOM同步功能了")
        else:
            print("\n❌ BOM同步测试失败，请检查错误信息")
    else:
        print("跳过完整测试")


if __name__ == "__main__":
    main()
