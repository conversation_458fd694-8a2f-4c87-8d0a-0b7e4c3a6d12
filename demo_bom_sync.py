#!/usr/bin/env python3
"""
BOM同步功能演示脚本
展示增量同步、占位行保留、排序字段等功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sync_bom_data import sync_bom_data_concurrently, convert_bom_record_with_mapping
from get_tvas_info import GetTvasInfo
import re

class MockTvasClient:
    """模拟TVAS客户端，用于演示"""
    def __init__(self, data_map):
        self.data_map = data_map
    
    def get_all_bom_data(self, vin):
        """模拟获取BOM数据，已经过去重处理"""
        return self.data_map.get(vin, [])

class MockBomTable:
    """模拟BOM表格，用于演示"""
    def __init__(self):
        self.records = []
        self.next_id = 1
    
    def get_all_records(self):
        return list(self.records)
    
    def batch_create(self, records):
        print(f"📝 批量创建 {len(records)} 条记录:")
        for r in records:
            fields = r.get("fields", {})
            record_id = f"rec_{self.next_id}"
            self.next_id += 1
            self.records.append({"record_id": record_id, "fields": fields})
            
            # 格式化显示
            vin = fields.get("vin", "")
            sort_idx = fields.get("sort_index", "")
            en_name = fields.get("en_name", "")
            if en_name:
                print(f"   ✅ {record_id}: VIN={vin}, 规格={en_name}, sort_index={sort_idx}")
            else:
                print(f"   📌 {record_id}: VIN={vin} (占位行), sort_index={sort_idx}")
        return True
    
    def batch_update(self, records):
        print(f"🔄 批量更新 {len(records)} 条记录:")
        for r in records:
            record_id = r.get("record_id")
            new_fields = r.get("fields", {})
            # 找到并更新记录
            for rec in self.records:
                if rec.get("record_id") == record_id:
                    rec["fields"].update(new_fields)
                    vin = rec["fields"].get("vin", "")
                    en_name = rec["fields"].get("en_name", "")
                    print(f"   🔄 {record_id}: VIN={vin}, 规格={en_name}")
                    break
        return True
    
    def batch_delete(self, record_ids):
        print(f"🗑️ 批量删除 {len(record_ids)} 条记录:")
        for rid in record_ids:
            # 找到要删除的记录
            for rec in self.records:
                if rec.get("record_id") == rid:
                    vin = rec["fields"].get("vin", "")
                    en_name = rec["fields"].get("en_name", "")
                    print(f"   ❌ {rid}: VIN={vin}, 规格={en_name}")
                    break
            self.records = [r for r in self.records if r.get("record_id") != rid]
        return True
    
    def show_current_state(self):
        """显示当前表格状态"""
        print(f"\n📊 当前BOM表格状态 (共 {len(self.records)} 条记录):")
        if not self.records:
            print("   (空表格)")
            return
        
        # 按 sort_index 排序显示
        sorted_records = sorted(self.records, key=lambda x: x["fields"].get("sort_index", 999))
        for i, rec in enumerate(sorted_records, 1):
            fields = rec["fields"]
            vin = fields.get("vin", "")
            sort_idx = fields.get("sort_index", "")
            en_name = fields.get("en_name", "")
            
            if en_name:
                print(f"   {i}. VIN={vin}, 规格={en_name}, sort_index={sort_idx}")
            else:
                print(f"   {i}. VIN={vin} (占位行), sort_index={sort_idx}")

def demo_bom_sync():
    """演示BOM同步功能"""
    print("🚀 BOM数据同步功能演示")
    print("=" * 50)
    
    # 初始化模拟环境
    bom_table = MockBomTable()
    
    # 第一次运行：初始数据
    print("\n🔄 第一次同步:")
    print("-" * 30)
    
    tvas = MockTvasClient({
        "VIN001": [{"en_name": "WHEELS ASSY _ 245/45R20 _ G1.1", "part_number": "P001"}],
        "VIN002": [],  # 无匹配，应创建占位行
        "VIN003": [{"en_name": "TIRE-245/50 R20 9J", "part_number": "P002"}]
    })
    
    vehicle_info = {
        "VIN001": {"fields": {}},
        "VIN002": {"fields": {}},
        "VIN003": {"fields": {}}
    }
    
    sync_bom_data_concurrently(vehicle_info, tvas, max_workers=1, bom_table=bom_table)
    bom_table.show_current_state()
    
    # 第二次运行：数据变化
    print("\n🔄 第二次同步 (数据变化):")
    print("-" * 30)
    print("变化说明:")
    print("- VIN001: 规格从 245/45R20 变为 245/50R20")
    print("- VIN002: 仍无匹配 (应跳过，保留占位行)")
    print("- VIN003: 被删除 (应清理规格记录)")
    print("- VIN004: 新增")
    
    tvas.data_map = {
        "VIN001": [{"en_name": "TIRE-245/50R20 NEW", "part_number": "P003"}],  # 规格变化
        "VIN002": [],  # 仍无匹配
        "VIN004": [{"en_name": "WHEEL 245/55R20", "part_number": "P004"}]  # 新增
    }
    
    vehicle_info = {
        "VIN001": {"fields": {}},
        "VIN002": {"fields": {}},
        "VIN004": {"fields": {}}
    }
    
    sync_bom_data_concurrently(vehicle_info, tvas, max_workers=1, bom_table=bom_table)
    bom_table.show_current_state()
    
    print("\n✅ 演示完成!")
    print("\n📋 功能总结:")
    print("1. ✅ 占位行保留 - VIN002的占位行在第二次运行时被保留")
    print("2. ✅ 规格更新 - VIN001的规格从245/45R20更新为245/50R20")
    print("3. ✅ 自动清理 - VIN003被删除后，其规格记录被自动清理")
    print("4. ✅ 新增记录 - VIN004的新规格记录被正确添加")
    print("5. ✅ 排序字段 - sort_index确保占位行(0)在规格行(1)之前")

if __name__ == "__main__":
    demo_bom_sync()
