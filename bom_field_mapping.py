#!/usr/bin/python3
"""
BOM字段映射配置
基于飞书表格实际字段生成
生成时间: 2025-08-08 18:17:46
"""

def get_bom_field_mapping():
    """获取BOM字段映射配置"""
    return {
        "vin": "vin",
        "part_number": "part_number",
        "en_name": "en_name",
        "cn_name": "cn_name",
        "quantity": "quantity",
        "uom": "uom"
}

def convert_bom_record(tvas_record, vin):
    """转换BOM记录格式"""
    import datetime
    
    mapping = get_bom_field_mapping()
    formatted_record = {}
    
    # 处理时间字段
    def format_timestamp(timestamp):
        if not timestamp:
            return ""
        try:
            dt = datetime.datetime.fromtimestamp(int(timestamp))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return str(timestamp)
    
    # 映射字段
    for tvas_field, feishu_field in mapping.items():
        if tvas_field == "vin":
            formatted_record[feishu_field] = vin
        elif tvas_field == "create_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("create_time"))
        elif tvas_field == "modify_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("modify_time"))
        else:
            value = tvas_record.get(tvas_field, "")
            formatted_record[feishu_field] = str(value) if value is not None else ""
    
    return formatted_record

# 使用示例:
# from bom_field_mapping import convert_bom_record
# formatted = convert_bom_record(tvas_data, "VIN123456")
