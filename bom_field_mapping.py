#!/usr/bin/python3
"""
BOM字段映射配置
自动生成时间: 2025-08-08 18:21:58
基于飞书表格实际字段结构
"""

def get_bom_field_mapping():
    """获取BOM字段映射配置"""
    return {
        "vin": "vin",
        "part_number": "part_number",
        "en_name": "en_name",
        "cn_name": "cn_name",
        "quantity": "quantity",
        "uom": "uom",
        "partition_code": "partition_code",
        "creator": "creator",
        "create_time": "create_time",
        "modifier": "modifier",
        "modify_time": "modify_time",
        "status": "status"
}

def convert_bom_record(tvas_record, vin):
    """转换BOM记录格式"""
    import datetime
    
    mapping = get_bom_field_mapping()
    formatted_record = {}
    
    def format_timestamp(timestamp):
        """格式化时间戳"""
        if not timestamp:
            return ""
        try:
            dt = datetime.datetime.fromtimestamp(int(timestamp))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return str(timestamp)
    
    # 映射字段
    for tvas_field, feishu_field in mapping.items():
        if tvas_field == "vin":
            formatted_record[feishu_field] = vin
        elif tvas_field == "create_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("create_time"))
        elif tvas_field == "modify_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("modify_time"))
        elif tvas_field == "quantity":
            # 确保数量为字符串
            formatted_record[feishu_field] = str(tvas_record.get(tvas_field, ""))
        else:
            value = tvas_record.get(tvas_field, "")
            formatted_record[feishu_field] = str(value) if value is not None else ""
    
    return formatted_record

# 字段映射信息
FIELD_MAPPING = get_bom_field_mapping()
MAPPED_FIELDS = list(FIELD_MAPPING.keys())
FEISHU_FIELDS = list(FIELD_MAPPING.values())

print("可用的TVAS字段:", MAPPED_FIELDS)
print("对应的飞书字段:", FEISHU_FIELDS)
