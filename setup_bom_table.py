#!/usr/bin/python3
"""
设置BOM表格字段和测试数据插入
用于初始化BOM表格或验证字段映射
"""

import json
import datetime
import config
import vehicle_sw_version_table
from log_config import logger


def create_test_bom_record():
    """创建测试用的BOM记录"""
    
    # 基于TVAS数据结构创建测试记录
    test_record = {
        "vin": "TEST_VIN_123456",
        "part_number": "P0373183", 
        "part_revision": "AC",
        "en_name": "ANTENNA-GNSS MAIN",
        "cn_name": "GNSS 主天线",
        "quantity": "1",
        "uom": "EA", 
        "partition_code": "ELE.10.04.05",
        "creator": "VINBOM",
        "create_time": "2025-08-08 18:08:04",
        "modifier": "VINBOM", 
        "modify_time": "2025-08-08 18:08:04",
        "status": "Assembled"
    }
    
    return test_record


def test_field_insertion():
    """测试字段插入，找出正确的字段名"""
    
    print("=" * 80)
    print("BOM表格字段测试")
    print("=" * 80)
    
    # 创建测试记录
    test_record = create_test_bom_record()
    print("📋 测试记录:")
    print(json.dumps(test_record, ensure_ascii=False, indent=2))
    
    # 初始化BOM表格
    bom_table = vehicle_sw_version_table.VehicleBom()
    
    print("\n🔍 开始测试字段插入...")
    
    # 尝试插入完整记录
    print("\n1. 尝试插入完整记录:")
    try:
        success = bom_table.create_record(test_record)
        if success:
            print("✅ 完整记录插入成功！所有字段名都正确")
            return True
        else:
            print("❌ 完整记录插入失败，开始逐个字段测试")
    except Exception as e:
        print(f"❌ 完整记录插入异常: {str(e)}")
    
    # 逐个字段测试
    print("\n2. 逐个字段测试:")
    working_fields = {}
    
    for field_name, field_value in test_record.items():
        print(f"\n测试字段: {field_name} = {field_value}")
        
        single_field_record = {field_name: field_value}
        
        try:
            success = bom_table.create_record(single_field_record)
            if success:
                print(f"✅ 字段 '{field_name}' 插入成功")
                working_fields[field_name] = field_value
                
                # 删除测试记录
                try:
                    # 获取刚插入的记录并删除
                    for record in bom_table.get_all_records():
                        if record.get("fields", {}).get(field_name) == field_value:
                            bom_table.delete_record(record.get("record_id"))
                            break
                except:
                    pass
            else:
                print(f"❌ 字段 '{field_name}' 插入失败")
        except Exception as e:
            print(f"❌ 字段 '{field_name}' 插入异常: {str(e)}")
    
    print(f"\n📊 测试结果:")
    print(f"成功字段数: {len(working_fields)}/{len(test_record)}")
    
    if working_fields:
        print("\n✅ 可用字段:")
        for field_name in working_fields:
            print(f"  - {field_name}")
    
    return len(working_fields) > 0


def suggest_field_mapping_from_error(error_message):
    """根据错误信息推测正确的字段名"""
    
    print("\n🔍 错误信息分析:")
    print(f"错误: {error_message}")
    
    # 常见的字段名变体
    field_variants = {
        "vin": ["vin", "VIN", "车辆识别码", "车架号"],
        "part_number": ["part_number", "partNumber", "零件号", "part_no", "pn"],
        "part_revision": ["part_revision", "partRevision", "零件版本", "revision", "rev"],
        "en_name": ["en_name", "enName", "英文名称", "english_name", "name_en"],
        "cn_name": ["cn_name", "cnName", "中文名称", "chinese_name", "name_cn"],
        "quantity": ["quantity", "数量", "qty", "amount"],
        "uom": ["uom", "UOM", "单位", "unit"],
        "partition_code": ["partition_code", "partitionCode", "分区代码", "partition"],
        "creator": ["creator", "创建者", "created_by"],
        "create_time": ["create_time", "createTime", "创建时间", "created_at"],
        "modifier": ["modifier", "修改者", "modified_by"],
        "modify_time": ["modify_time", "modifyTime", "修改时间", "modified_at"],
        "status": ["status", "状态", "state"]
    }
    
    print("\n💡 建议的字段名变体:")
    for original, variants in field_variants.items():
        print(f"{original:15} → {', '.join(variants)}")


def interactive_field_mapping():
    """交互式字段映射设置"""
    
    print("\n" + "=" * 80)
    print("交互式字段映射设置")
    print("=" * 80)
    
    print("请查看飞书BOM表格，确认实际的字段名称")
    print("表格链接: https://nio.feishu.cn/base/XuG3bU8uDaR4Nts7geLcTO1Jnhh?table=tblvLdUKy6MRZXPX&view=vewI62gWSb")
    
    expected_fields = [
        "vin", "part_number", "part_revision", "en_name", "cn_name",
        "quantity", "uom", "partition_code", "creator", "create_time", 
        "modifier", "modify_time", "status"
    ]
    
    field_mapping = {}
    
    print(f"\n请为以下 {len(expected_fields)} 个字段提供飞书表格中的实际字段名:")
    print("(如果字段不存在，请输入 'skip' 跳过)")
    
    for field in expected_fields:
        while True:
            actual_name = input(f"\n{field:15} → ").strip()
            
            if actual_name.lower() == 'skip':
                print(f"跳过字段: {field}")
                break
            elif actual_name:
                field_mapping[field] = actual_name
                print(f"映射: {field} → {actual_name}")
                break
            else:
                print("请输入有效的字段名或 'skip'")
    
    # 生成映射代码
    print("\n" + "=" * 80)
    print("生成的字段映射代码")
    print("=" * 80)
    
    print("```python")
    print("def get_safe_field_mapping():")
    print("    return {")
    for original, actual in field_mapping.items():
        print(f'        "{original}": "{actual}",')
    print("    }")
    print("```")
    
    # 保存映射到文件
    mapping_code = f"""# BOM字段映射配置
# 生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

def get_bom_field_mapping():
    \"\"\"获取BOM字段映射配置\"\"\"
    return {{
{chr(10).join([f'        "{original}": "{actual}",' for original, actual in field_mapping.items()])}
    }}

# 使用示例:
# mapping = get_bom_field_mapping()
# formatted_record = {{mapping[field]: value for field, value in tvas_record.items() if field in mapping}}
"""
    
    with open("bom_field_mapping.py", "w", encoding="utf-8") as f:
        f.write(mapping_code)
    
    print(f"\n✅ 字段映射已保存到: bom_field_mapping.py")
    
    return field_mapping


def main():
    """主函数"""
    
    print("BOM表格设置和字段映射工具")
    print("=" * 80)
    
    print("\n选择操作:")
    print("1. 测试字段插入（自动检测可用字段）")
    print("2. 交互式设置字段映射")
    print("3. 查看错误信息分析")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == "1":
        print("\n开始自动字段测试...")
        if test_field_insertion():
            print("\n✅ 找到可用字段，可以继续使用")
        else:
            print("\n❌ 未找到可用字段，建议使用选项2进行手动映射")
            
    elif choice == "2":
        print("\n开始交互式字段映射...")
        mapping = interactive_field_mapping()
        if mapping:
            print(f"\n✅ 完成字段映射，共映射 {len(mapping)} 个字段")
        
    elif choice == "3":
        error_msg = input("\n请粘贴错误信息: ").strip()
        if error_msg:
            suggest_field_mapping_from_error(error_msg)
        else:
            print("未提供错误信息")
    
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
