#!/usr/bin/python3
"""
BOM数据同步详细示例
展示完整的BOM数据获取、处理和同步流程
"""

import json
import time
import config
import login_nio
import get_tvas_info
import vehicle_sw_version_table
from log_config import logger


def demonstrate_bom_sync_process():
    """演示完整的BOM数据同步流程"""
    
    print("=" * 80)
    print("BOM数据同步流程演示")
    print("=" * 80)
    
    # 示例VIN
    test_vin = "HJNAAJNC8SA050133"
    
    try:
        # 步骤1: 系统登录
        print("\n步骤1: 登录TVAS系统")
        print("-" * 40)
        
        def login_system(url, element_id):
            login = login_nio.LoginNio(url, element_id)
            return login.get_cookie()
        
        tvas_cookie = login_system("https://tvas.nioint.com/tvas/vehicleResource", "container")
        tvas_client = get_tvas_info.GetTvasInfo(tvas_cookie)
        print("✓ TVAS系统登录成功")
        
        # 步骤2: 获取车辆信息
        print("\n步骤2: 从飞书汇总表获取车辆信息")
        print("-" * 40)
        
        summary_table = vehicle_sw_version_table.Summary()
        vehicle_info = summary_table.get_vehicle_info_dict()
        print(f"✓ 获取到 {len(vehicle_info)} 辆车的信息")
        
        # 显示前3个VIN作为示例
        vin_list = list(vehicle_info.keys())[:3]
        for i, vin in enumerate(vin_list):
            info = vehicle_info[vin]
            print(f"  车辆 {i+1}: VIN={vin}")
            print(f"    记录ID: {info.get('record_id')}")
            print(f"    使用DID: {info.get('use_did')}")
            print(f"    车型: {info.get('vehicle_model')}")
        
        # 步骤3: 获取BOM数据
        print(f"\n步骤3: 从TVAS获取VIN {test_vin} 的BOM数据")
        print("-" * 40)
        
        # 获取第一页数据查看总数
        first_page = tvas_client.query_bom_by_vin(test_vin, page_size=10, page_count=1)
        if first_page:
            total_count = first_page.get("total_count", 0)
            print(f"✓ VIN {test_vin} 总共有 {total_count} 条BOM记录")
            
            # 显示前3条记录
            records = first_page.get("list", [])
            print(f"✓ 获取到第一页 {len(records)} 条记录，示例:")
            
            for i, record in enumerate(records[:3]):
                print(f"  BOM记录 {i+1}:")
                print(f"    零件号: {record.get('part_number')} {record.get('part_revision')}")
                print(f"    英文名: {record.get('en_name')}")
                print(f"    中文名: {record.get('cn_name')}")
                print(f"    数量: {record.get('quantity')} {record.get('uom')}")
                print(f"    分区: {record.get('partition_code')}")
                print(f"    状态: {record.get('status')}")
        else:
            print(f"✗ 无法获取VIN {test_vin} 的BOM数据")
            return
        
        # 步骤4: 数据格式转换
        print(f"\n步骤4: 数据格式转换")
        print("-" * 40)
        
        # 获取所有BOM数据
        all_bom_records = tvas_client.get_all_bom_data(test_vin)
        print(f"✓ 获取到完整BOM数据: {len(all_bom_records)} 条记录")
        
        # 转换格式
        formatted_records = []
        for bom_record in all_bom_records:
            create_time = bom_record.get("create_time")
            modify_time = bom_record.get("modify_time")
            create_time_ms = int(create_time) * 1000 if create_time else None
            modify_time_ms = int(modify_time) * 1000 if modify_time else None
            
            formatted_record = {
                "vin": test_vin,
                "part_number": bom_record.get("part_number", ""),
                "part_revision": bom_record.get("part_revision", ""),
                "en_name": bom_record.get("en_name", ""),
                "cn_name": bom_record.get("cn_name", ""),
                "quantity": bom_record.get("quantity", ""),
                "uom": bom_record.get("uom", ""),
                "partition_code": bom_record.get("partition_code", ""),
                "creator": bom_record.get("creator", ""),
                "create_time": create_time_ms,
                "modifier": bom_record.get("modifier", ""),
                "modify_time": modify_time_ms,
                "status": bom_record.get("status", "")
            }
            formatted_records.append(formatted_record)
        
        print(f"✓ 格式转换完成: {len(formatted_records)} 条记录")
        
        # 显示转换后的示例数据
        if formatted_records:
            sample_record = formatted_records[0]
            print("✓ 转换后的数据格式示例:")
            print(json.dumps(sample_record, ensure_ascii=False, indent=2))
        
        # 步骤5: 飞书表格操作
        print(f"\n步骤5: 同步到飞书BOM表格")
        print("-" * 40)
        
        bom_table = vehicle_sw_version_table.VehicleBom()
        
        # 检查现有数据
        existing_vins = bom_table.get_existing_bom_vins()
        print(f"✓ BOM表中已存在 {len(existing_vins)} 个VIN的数据")
        
        if test_vin in existing_vins:
            print(f"✓ VIN {test_vin} 已存在，将删除旧数据")
            bom_table.delete_bom_records_by_vin(test_vin)
        
        # 批量插入新数据
        print(f"✓ 开始插入 {len(formatted_records)} 条BOM记录...")
        formatted_for_insert = [{"fields": record} for record in formatted_records]
        
        start_time = time.time()
        success = bom_table.batch_create(formatted_for_insert)
        end_time = time.time()
        
        if success:
            print(f"✓ 批量插入成功，耗时: {end_time - start_time:.2f} 秒")
        else:
            print("✗ 批量插入失败，尝试逐条插入...")
            success_count = 0
            for record in formatted_for_insert:
                try:
                    if bom_table.create_record(record.get("fields")):
                        success_count += 1
                except Exception as e:
                    logger.error(f"单条插入失败: {e}")
            print(f"✓ 逐条插入完成: {success_count}/{len(formatted_for_insert)}")
        
        # 步骤6: 验证结果
        print(f"\n步骤6: 验证同步结果")
        print("-" * 40)
        
        # 重新检查BOM表中的VIN
        updated_vins = bom_table.get_existing_bom_vins()
        if test_vin in updated_vins:
            print(f"✓ VIN {test_vin} 已成功同步到BOM表")
        else:
            print(f"✗ VIN {test_vin} 同步失败")
        
        print(f"✓ BOM表现在包含 {len(updated_vins)} 个VIN的数据")
        
        print("\n" + "=" * 80)
        print("BOM数据同步流程演示完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"✗ 演示过程中发生错误: {str(e)}")
        logger.error(f"BOM同步演示失败: {str(e)}")


def show_bom_data_structure():
    """展示BOM数据结构和字段说明"""
    
    print("\n" + "=" * 80)
    print("BOM数据结构说明")
    print("=" * 80)
    
    print("\n1. TVAS原始数据字段:")
    print("-" * 40)
    fields = [
        ("id", "记录ID", "4093696"),
        ("vin", "车辆识别码", "HJNAAJNC8SA050133"),
        ("part_number", "零件号", "P0373183"),
        ("part_revision", "零件版本", "AC"),
        ("en_name", "英文名称", "ANTENNA-GNSS MAIN"),
        ("cn_name", "中文名称", "GNSS 主天线"),
        ("quantity", "数量", "1"),
        ("uom", "单位", "EA"),
        ("partition_code", "分区代码", "ELE.10.04.05"),
        ("creator", "创建者", "VINBOM"),
        ("create_time", "创建时间", "1754543582 (秒级时间戳)"),
        ("modifier", "修改者", "VINBOM"),
        ("modify_time", "修改时间", "1754543582 (秒级时间戳)"),
        ("status", "状态", "Assembled")
    ]
    
    for field, desc, example in fields:
        print(f"  {field:15} | {desc:12} | {example}")
    
    print("\n2. 飞书表格字段映射:")
    print("-" * 40)
    print("  所有字段直接映射，时间戳转换为毫秒级")
    print("  create_time: 1754543582 → 1754543582000")
    print("  modify_time: 1754543582 → 1754543582000")
    
    print("\n3. 数据处理特点:")
    print("-" * 40)
    print("  • 支持分页获取，自动处理大量数据")
    print("  • 智能去重，避免重复同步")
    print("  • 批量操作，提高同步效率")
    print("  • 错误恢复，确保数据完整性")


if __name__ == "__main__":
    # 显示数据结构说明
    show_bom_data_structure()
    
    # 询问是否运行演示
    print("\n是否运行BOM数据同步演示？(y/n, 默认n): ", end="")
    user_input = input().strip().lower()
    
    if user_input in ['y', 'yes']:
        demonstrate_bom_sync_process()
    else:
        print("演示已跳过。如需运行演示，请重新启动并选择 'y'")
