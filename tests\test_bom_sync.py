import unittest
from unittest.mock import MagicMock
import re

from sync_bom_data import convert_bom_record_with_mapping, sync_bom_data_for_vehicle

class FakeTvasClient:
    def __init__(self, data_by_vin):
        self.data_by_vin = data_by_vin
    def get_all_bom_data(self, vin):
        return self.data_by_vin.get(vin, [])

class TestBomSync(unittest.TestCase):
    def test_dedup_ignore_spaces_and_en_name_only_spec(self):
        vin = "LJ1EFAUU7MG073916"
        # 两条 en_name 规格相同，仅空格不同；另有一条不同规格
        records = [
            {"en_name": "WHEELS ASSY _ 245/45R20 _ G1.1", "part_number": "P1"},
            {"en_name": "TIRE-245/45 R20 9J", "part_number": "P2"},
            {"en_name": "TIRE-245/50R20 9J", "part_number": "P3"},
        ]
        # 模拟 get_all_bom_data 已经做了“按规格忽略空格去重”，这里只保留两个规格
        fake_tvas = FakeTvasClient({vin: [records[0], records[2]]})
        out = sync_bom_data_for_vehicle(vin, fake_tvas)
        # en_name 只保留正则规格，且去空格统一大写
        en_values = sorted([r["en_name"] for r in out if r.get("en_name")])
        self.assertEqual(en_values, ["245/45R20", "245/50R20"])  
        # VIN 必须保留
        self.assertTrue(all(r.get("vin") == vin for r in out))

    def test_placeholder_when_no_match(self):
        vin = "VIN_NO_MATCH"
        fake_tvas = FakeTvasClient({vin: []})
        out = sync_bom_data_for_vehicle(vin, fake_tvas)
        # 无匹配时应返回一条包含 vin 和 sort_index=0 的占位行
        self.assertEqual(len(out), 1)
        self.assertEqual(set(out[0].keys()), {"vin", "sort_index"})
        self.assertEqual(out[0]["vin"], vin)
        self.assertEqual(out[0]["sort_index"], 0)

    def test_convert_only_keeps_spec_in_en_name(self):
        vin = "VIN_SPEC"
        rec = {"en_name": "WHEELS ASSY _ 245/45 R20 _ ABC"}
        formatted = convert_bom_record_with_mapping(rec, vin)
        self.assertEqual(formatted["en_name"], "245/45R20")
        self.assertEqual(formatted["vin"], vin)
        self.assertEqual(formatted["sort_index"], 1)

if __name__ == '__main__':
    unittest.main()

