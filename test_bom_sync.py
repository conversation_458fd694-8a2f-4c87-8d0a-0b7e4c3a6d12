#!/usr/bin/python3
"""
BOM数据同步测试脚本
用于测试单个VIN的BOM数据获取和同步功能
"""

import json
import config
import login_nio
import get_tvas_info
import vehicle_sw_version_table
from log_config import logger


def test_single_vin_bom(test_vin: str = "HJNAAJNC8SA050133"):
    """
    测试单个VIN的BOM数据获取和同步
    :param test_vin: 测试用的VIN码
    """
    logger.info(f"开始测试VIN {test_vin} 的BOM数据同步")
    
    try:
        # 登录TVAS系统
        def login_system(url, element_id):
            """通用登录函数"""
            login = login_nio.LoginNio(url, element_id)
            return login.get_cookie()
        
        logger.info("登录TVAS系统...")
        tvas_cookie = login_system("https://tvas.nioint.com/tvas/vehicleResource", "container")
        tvas_client = get_tvas_info.GetTvasInfo(tvas_cookie)
        
        # 测试BOM数据获取
        logger.info(f"获取VIN {test_vin} 的BOM数据...")
        bom_data = tvas_client.query_bom_by_vin(test_vin, page_size=10, page_count=1)
        
        if bom_data:
            logger.info(f"成功获取BOM数据，总记录数: {bom_data.get('total_count', 0)}")
            logger.info(f"当前页记录数: {len(bom_data.get('list', []))}")
            
            # 显示前3条记录作为示例
            records = bom_data.get('list', [])
            for i, record in enumerate(records[:3]):
                logger.info(f"BOM记录 {i+1}:")
                logger.info(f"  零件号: {record.get('part_number')} {record.get('part_revision')}")
                logger.info(f"  英文名: {record.get('en_name')}")
                logger.info(f"  中文名: {record.get('cn_name')}")
                logger.info(f"  数量: {record.get('quantity')} {record.get('uom')}")
                logger.info(f"  分区代码: {record.get('partition_code')}")
                logger.info(f"  状态: {record.get('status')}")
                logger.info("  ---")
        else:
            logger.error(f"未能获取VIN {test_vin} 的BOM数据")
            return
        
        # 测试获取所有BOM数据
        logger.info("获取所有BOM数据...")
        all_bom_records = tvas_client.get_all_bom_data(test_vin)
        logger.info(f"总共获取到 {len(all_bom_records)} 条BOM记录")
        
        # 测试数据格式转换
        logger.info("测试数据格式转换...")
        formatted_records = []
        for bom_record in all_bom_records[:5]:  # 只转换前5条作为测试
            create_time = bom_record.get("create_time")
            modify_time = bom_record.get("modify_time")
            create_time_ms = int(create_time) * 1000 if create_time else None
            modify_time_ms = int(modify_time) * 1000 if modify_time else None
            
            formatted_record = {
                "vin": test_vin,
                "part_number": bom_record.get("part_number", ""),
                "part_revision": bom_record.get("part_revision", ""),
                "en_name": bom_record.get("en_name", ""),
                "cn_name": bom_record.get("cn_name", ""),
                "quantity": bom_record.get("quantity", ""),
                "uom": bom_record.get("uom", ""),
                "partition_code": bom_record.get("partition_code", ""),
                "creator": bom_record.get("creator", ""),
                "create_time": create_time_ms,
                "modifier": bom_record.get("modifier", ""),
                "modify_time": modify_time_ms,
                "status": bom_record.get("status", "")
            }
            formatted_records.append(formatted_record)
        
        logger.info("格式转换完成，示例记录:")
        for i, record in enumerate(formatted_records):
            logger.info(f"格式化记录 {i+1}: {json.dumps(record, ensure_ascii=False, indent=2)}")
        
        # 测试飞书表格操作
        logger.info("测试飞书BOM表格操作...")
        bom_table = vehicle_sw_version_table.VehicleBom()
        
        # 检查现有VIN
        existing_vins = bom_table.get_existing_bom_vins()
        logger.info(f"BOM表中已存在的VIN数量: {len(existing_vins)}")
        
        if test_vin in existing_vins:
            logger.info(f"VIN {test_vin} 已存在于BOM表中")
        else:
            logger.info(f"VIN {test_vin} 不存在于BOM表中")
        
        # 测试插入一条记录
        if formatted_records:
            test_record = formatted_records[0]
            logger.info("测试插入单条BOM记录...")
            success = bom_table.create_record(test_record)
            if success:
                logger.info("单条BOM记录插入成功")
            else:
                logger.error("单条BOM记录插入失败")
        
        logger.info(f"VIN {test_vin} BOM数据测试完成")
        
    except Exception as e:
        logger.error(f"测试VIN {test_vin} BOM数据失败: {str(e)}")


def main():
    """测试主函数"""
    logger.info("开始BOM数据同步测试")
    
    # 可以修改这个VIN来测试不同的车辆
    test_vin = "HJNAAJNC8SA050133"  # 您提供的示例VIN
    
    # 也可以测试其他VIN
    # test_vin = input("请输入要测试的VIN（直接回车使用默认VIN）: ").strip()
    # if not test_vin:
    #     test_vin = "HJNAAJNC8SA050133"
    
    test_single_vin_bom(test_vin)


if __name__ == "__main__":
    main()
