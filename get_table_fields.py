#!/usr/bin/python3
"""
直接获取飞书表格的所有字段信息
最简单直接的方法
"""

import json
import config
import lark_requests
from log_config import logger


def get_all_table_fields():
    """获取BOM表格的所有字段信息"""
    
    print("=" * 80)
    print("获取飞书BOM表格的所有字段")
    print("=" * 80)
    
    try:
        # 初始化飞书请求客户端
        lark_client = lark_requests.LarkRequests(config.APP_TOKEN, config.BOM_TABLE_ID)
        
        print("📋 正在获取表格字段信息...")
        
        # 获取表格的字段信息
        fields_info = lark_client.get_table_fields()
        
        if not fields_info:
            print("❌ 无法获取表格字段信息")
            return None
        
        print(f"✅ 成功获取表格字段信息，共 {len(fields_info)} 个字段")
        
        print("\n" + "=" * 80)
        print("字段详细信息")
        print("=" * 80)
        
        field_mapping = {}
        
        for i, field in enumerate(fields_info, 1):
            field_id = field.get('field_id', '')
            field_name = field.get('field_name', '')
            field_type = field.get('type', '')
            
            print(f"\n字段 {i:2d}:")
            print(f"  ID:   {field_id}")
            print(f"  名称: {field_name}")
            print(f"  类型: {field_type}")
            
            # 构建字段映射（使用字段名作为键）
            if field_name:
                # 尝试匹配TVAS字段
                tvas_field = match_tvas_field(field_name)
                if tvas_field:
                    field_mapping[tvas_field] = field_name
                    print(f"  映射: {tvas_field} → {field_name}")
        
        print("\n" + "=" * 80)
        print("字段映射汇总")
        print("=" * 80)
        
        print("找到的字段映射:")
        for tvas_field, feishu_field in field_mapping.items():
            print(f"  {tvas_field:15} → {feishu_field}")
        
        # 检查缺失的字段
        expected_fields = [
            "vin", "part_number", "part_revision", "en_name", "cn_name",
            "quantity", "uom", "partition_code", "creator", "create_time",
            "modifier", "modify_time", "status"
        ]
        
        missing_fields = [f for f in expected_fields if f not in field_mapping]
        if missing_fields:
            print(f"\n未找到映射的字段: {', '.join(missing_fields)}")
            
            print("\n所有飞书字段名:")
            all_field_names = [f.get('field_name', '') for f in fields_info if f.get('field_name')]
            for name in all_field_names:
                print(f"  - {name}")
        
        # 生成字段映射代码
        generate_mapping_code(field_mapping, all_field_names if missing_fields else None)
        
        return field_mapping
        
    except Exception as e:
        print(f"❌ 获取表格字段失败: {str(e)}")
        logger.error(f"获取表格字段失败: {str(e)}")
        return None


def match_tvas_field(feishu_field_name):
    """匹配TVAS字段名"""
    
    # 字段名映射规则
    mapping_rules = {
        # 精确匹配
        "vin": "vin",
        "VIN": "vin",
        "part_number": "part_number",
        "partNumber": "part_number",
        "part_revision": "part_revision", 
        "partRevision": "part_revision",
        "en_name": "en_name",
        "enName": "en_name",
        "cn_name": "cn_name",
        "cnName": "cn_name",
        "quantity": "quantity",
        "uom": "uom",
        "UOM": "uom",
        "partition_code": "partition_code",
        "partitionCode": "partition_code",
        "creator": "creator",
        "create_time": "create_time",
        "createTime": "create_time",
        "modifier": "modifier",
        "modify_time": "modify_time",
        "modifyTime": "modify_time",
        "status": "status",
        
        # 中文匹配
        "车辆识别码": "vin",
        "车架号": "vin",
        "零件号": "part_number",
        "零件编号": "part_number",
        "零件版本": "part_revision",
        "版本": "part_revision",
        "英文名称": "en_name",
        "英文名": "en_name",
        "中文名称": "cn_name",
        "中文名": "cn_name",
        "数量": "quantity",
        "单位": "uom",
        "分区代码": "partition_code",
        "分区": "partition_code",
        "创建者": "creator",
        "创建人": "creator",
        "创建时间": "create_time",
        "修改者": "modifier",
        "修改人": "modifier",
        "修改时间": "modify_time",
        "状态": "status"
    }
    
    # 精确匹配
    if feishu_field_name in mapping_rules:
        return mapping_rules[feishu_field_name]
    
    # 模糊匹配
    feishu_lower = feishu_field_name.lower()
    for feishu_pattern, tvas_field in mapping_rules.items():
        if feishu_pattern.lower() in feishu_lower or feishu_lower in feishu_pattern.lower():
            return tvas_field
    
    return None


def generate_mapping_code(field_mapping, all_field_names=None):
    """生成字段映射代码"""
    
    print("\n" + "=" * 80)
    print("生成字段映射代码")
    print("=" * 80)
    
    # 生成Python代码
    mapping_code = f'''#!/usr/bin/python3
"""
BOM字段映射配置
自动生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
基于飞书表格实际字段结构
"""

def get_bom_field_mapping():
    """获取BOM字段映射配置"""
    return {json.dumps(field_mapping, ensure_ascii=False, indent=8)}

def convert_bom_record(tvas_record, vin):
    """转换BOM记录格式"""
    import datetime
    
    mapping = get_bom_field_mapping()
    formatted_record = {{}}
    
    def format_timestamp(timestamp):
        """格式化时间戳"""
        if not timestamp:
            return ""
        try:
            dt = datetime.datetime.fromtimestamp(int(timestamp))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return str(timestamp)
    
    # 映射字段
    for tvas_field, feishu_field in mapping.items():
        if tvas_field == "vin":
            formatted_record[feishu_field] = vin
        elif tvas_field == "create_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("create_time"))
        elif tvas_field == "modify_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("modify_time"))
        elif tvas_field == "quantity":
            # 确保数量为字符串
            formatted_record[feishu_field] = str(tvas_record.get(tvas_field, ""))
        else:
            value = tvas_record.get(tvas_field, "")
            formatted_record[feishu_field] = str(value) if value is not None else ""
    
    return formatted_record

# 字段映射信息
FIELD_MAPPING = get_bom_field_mapping()
MAPPED_FIELDS = list(FIELD_MAPPING.keys())
FEISHU_FIELDS = list(FIELD_MAPPING.values())

print("可用的TVAS字段:", MAPPED_FIELDS)
print("对应的飞书字段:", FEISHU_FIELDS)
'''
    
    # 保存到文件
    with open("bom_field_mapping.py", "w", encoding="utf-8") as f:
        f.write(mapping_code)
    
    print("✅ 字段映射代码已保存到: bom_field_mapping.py")
    
    if all_field_names:
        print("\n💡 如果需要手动映射缺失字段，所有可用的飞书字段名:")
        for name in all_field_names:
            print(f"  '{name}'")


def main():
    """主函数"""
    
    print("直接获取飞书BOM表格的所有字段信息")
    
    field_mapping = get_all_table_fields()
    
    if field_mapping:
        print(f"\n🎉 成功获取字段映射，共 {len(field_mapping)} 个字段")
        print("现在可以使用生成的 bom_field_mapping.py 进行BOM数据同步")
    else:
        print("\n❌ 获取字段映射失败")


if __name__ == "__main__":
    main()
