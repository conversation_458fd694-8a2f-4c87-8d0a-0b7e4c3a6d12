#!/usr/bin/python3
"""
基于截图字段的精确测试
测试飞书表格中实际可见的字段
"""

import json
import datetime
import config
import vehicle_sw_version_table
from log_config import logger


def test_visible_fields_only():
    """测试截图中可见的字段"""
    
    print("=" * 80)
    print("测试飞书表格中可见的字段")
    print("=" * 80)
    
    # 基于截图的可见字段
    visible_fields = {
        "vin": "vin",
        "part_number": "part_number", 
        "en_name": "en_name",
        "cn_name": "cn_name",
        "quantity": "quantity",
        "uom": "uom"
    }
    
    # 测试数据（只包含可见字段）
    test_data = {
        "vin": "TEST123456",
        "part_number": "P0373183",
        "en_name": "ANTENNA-GNSS MAIN",
        "cn_name": "GNSS 主天线",
        "quantity": "1",
        "uom": "EA"
    }
    
    print("🧪 测试字段映射:")
    print(json.dumps(visible_fields, ensure_ascii=False, indent=2))
    
    print("\n📋 测试数据:")
    print(json.dumps(test_data, ensure_ascii=False, indent=2))
    
    # 构建测试记录
    test_record = {}
    for tvas_field, feishu_field in visible_fields.items():
        test_record[feishu_field] = test_data[tvas_field]
    
    print("\n📝 格式化记录:")
    print(json.dumps(test_record, ensure_ascii=False, indent=2))
    
    # 尝试插入
    bom_table = vehicle_sw_version_table.VehicleBom()
    
    try:
        print("\n🚀 尝试插入记录...")
        success = bom_table.create_record(test_record)
        
        if success:
            print("✅ 插入成功！")
            
            # 保存成功的映射
            save_working_mapping(visible_fields)
            
            # 清理测试数据
            cleanup_test_data(bom_table)
            
            return visible_fields
        else:
            print("❌ 插入失败")
            return None
            
    except Exception as e:
        print(f"❌ 插入异常: {str(e)}")
        
        # 分析错误信息
        error_str = str(e)
        if "FieldNameNotFound" in error_str:
            print("\n🔍 字段名未找到错误分析:")
            # 提取错误中的字段名
            import re
            field_match = re.search(r"'fields\.([^']+)'", error_str)
            if field_match:
                problem_field = field_match.group(1)
                print(f"问题字段: {problem_field}")
                
                # 建议替代字段名
                suggest_alternative_names(problem_field)
        
        return None


def suggest_alternative_names(problem_field):
    """建议替代字段名"""
    
    alternatives = {
        "vin": ["VIN", "车辆识别码", "车架号", "vin_code"],
        "part_number": ["partNumber", "零件号", "part_no", "pn", "零件编号"],
        "en_name": ["enName", "english_name", "英文名称", "name_en", "英文名"],
        "cn_name": ["cnName", "chinese_name", "中文名称", "name_cn", "中文名"],
        "quantity": ["qty", "数量", "amount", "count"],
        "uom": ["UOM", "unit", "单位", "measure_unit"]
    }
    
    if problem_field in alternatives:
        print(f"建议的替代字段名: {', '.join(alternatives[problem_field])}")
    else:
        print(f"未知字段: {problem_field}")


def test_individual_fields():
    """逐个测试字段"""
    
    print("\n" + "=" * 80)
    print("逐个字段测试")
    print("=" * 80)
    
    fields_to_test = {
        "vin": "TEST123456",
        "part_number": "P0373183", 
        "en_name": "ANTENNA-GNSS MAIN",
        "cn_name": "GNSS 主天线",
        "quantity": "1",
        "uom": "EA"
    }
    
    bom_table = vehicle_sw_version_table.VehicleBom()
    working_fields = {}
    
    for field_name, field_value in fields_to_test.items():
        print(f"\n🧪 测试字段: {field_name} = {field_value}")
        
        single_field_record = {field_name: field_value}
        
        try:
            success = bom_table.create_record(single_field_record)
            if success:
                print(f"✅ 字段 '{field_name}' 可用")
                working_fields[field_name] = field_name
                
                # 清理测试记录
                cleanup_single_field_test(bom_table, field_name, field_value)
            else:
                print(f"❌ 字段 '{field_name}' 不可用")
        except Exception as e:
            print(f"❌ 字段 '{field_name}' 异常: {str(e)}")
    
    if working_fields:
        print(f"\n✅ 可用字段: {list(working_fields.keys())}")
        save_working_mapping(working_fields)
        return working_fields
    else:
        print("\n❌ 没有找到可用字段")
        return None


def cleanup_single_field_test(bom_table, field_name, field_value):
    """清理单字段测试数据"""
    try:
        for record in bom_table.get_all_records():
            fields = record.get("fields", {})
            
            # 检查字段值
            if field_name in fields:
                record_value = fields[field_name]
                
                # 处理不同的字段值格式
                if isinstance(record_value, list) and len(record_value) > 0:
                    if record_value[0].get("text") == field_value:
                        bom_table.delete_record(record.get("record_id"))
                        break
                elif record_value == field_value:
                    bom_table.delete_record(record.get("record_id"))
                    break
    except Exception as e:
        logger.debug(f"清理单字段测试数据失败: {e}")


def cleanup_test_data(bom_table):
    """清理测试数据"""
    try:
        for record in bom_table.get_all_records():
            fields = record.get("fields", {})
            
            # 查找包含测试VIN的记录
            for field_name, field_value in fields.items():
                if isinstance(field_value, list) and len(field_value) > 0:
                    if field_value[0].get("text") == "TEST123456":
                        bom_table.delete_record(record.get("record_id"))
                        print("🧹 已清理测试数据")
                        return
                elif field_value == "TEST123456":
                    bom_table.delete_record(record.get("record_id"))
                    print("🧹 已清理测试数据")
                    return
    except Exception as e:
        logger.debug(f"清理测试数据失败: {e}")


def save_working_mapping(field_mapping):
    """保存有效的字段映射"""
    
    mapping_code = f'''#!/usr/bin/python3
"""
BOM字段映射配置
基于飞书表格实际字段生成
生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

def get_bom_field_mapping():
    """获取BOM字段映射配置"""
    return {json.dumps(field_mapping, ensure_ascii=False, indent=8)}

def convert_bom_record(tvas_record, vin):
    """转换BOM记录格式"""
    import datetime
    
    mapping = get_bom_field_mapping()
    formatted_record = {{}}
    
    # 处理时间字段
    def format_timestamp(timestamp):
        if not timestamp:
            return ""
        try:
            dt = datetime.datetime.fromtimestamp(int(timestamp))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return str(timestamp)
    
    # 映射字段
    for tvas_field, feishu_field in mapping.items():
        if tvas_field == "vin":
            formatted_record[feishu_field] = vin
        elif tvas_field == "create_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("create_time"))
        elif tvas_field == "modify_time":
            formatted_record[feishu_field] = format_timestamp(tvas_record.get("modify_time"))
        else:
            value = tvas_record.get(tvas_field, "")
            formatted_record[feishu_field] = str(value) if value is not None else ""
    
    return formatted_record

# 使用示例:
# from bom_field_mapping import convert_bom_record
# formatted = convert_bom_record(tvas_data, "VIN123456")
'''
    
    with open("bom_field_mapping.py", "w", encoding="utf-8") as f:
        f.write(mapping_code)
    
    print(f"✅ 字段映射已保存到: bom_field_mapping.py")


def main():
    """主函数"""
    
    print("基于飞书表格截图的精确字段测试")
    print("=" * 80)
    
    print("\n选择测试方式:")
    print("1. 测试所有可见字段（推荐）")
    print("2. 逐个字段测试")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == "1":
        print("\n开始测试所有可见字段...")
        mapping = test_visible_fields_only()
        if mapping:
            print("\n🎉 测试成功！字段映射已保存")
        else:
            print("\n❌ 测试失败，尝试逐个字段测试")
            mapping = test_individual_fields()
            
    elif choice == "2":
        print("\n开始逐个字段测试...")
        mapping = test_individual_fields()
        
    else:
        print("无效选择")
        return
    
    if mapping:
        print(f"\n✅ 找到 {len(mapping)} 个可用字段")
        print("现在可以使用生成的 bom_field_mapping.py 进行BOM数据同步")
    else:
        print("\n❌ 未找到可用字段，请检查飞书表格配置")


if __name__ == "__main__":
    main()
