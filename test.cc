#include <iostream>
#include <vector>
using namespace std;

class Solution 
{
public:
    int numSquares(int n) 
    {
        vector<int> dp(n + 1, INT_MAX);
        dp[0] = 0; // 初始化

        for(int i = 1; i <= n; i++)
            for(int j = 1; j * j <= i; j++)
                dp[i] = min(dp[i], dp[i - j * j] + 1);

        return dp[n];
    }
};

class Solution 
{
public:
    int majorityElement(vector<int>& nums) 
    {
        int n = nums.size();
        unordered_map<int, int> cnt;
        for(auto& x : nums)
            if(++cnt[x] > n / 2) return x;
        return -1;
    }
};
// 进阶：尝试设计时间复杂度为 O(n)、空间复杂度为 O(1) 的算法解决此问题。

class Solution 
{
public:
    int majorityElement(vector<int>& nums) 
    {
        int candidate = nums[0], cnt = 1;
        for(int i = 1; i < nums.size(); i++)
        {
            if(nums[i] == candidate) cnt++;
            else
            {
                cnt--;
                if(cnt == 0) 
                {
                    candidate = nums[i];
                    cnt = 1;
                }
            }
        }
        return candidate;
    }
};


class Solution 
{
public:
    void sortColors(vector<int>& nums) 
    {
        int left = -1, right = nums.size(), i = 0;
        while(i < nums.size())
        {
            if(nums[i] == 0) swap(nums[i++], nums[++left]);
            else if(nums[i] == 1) i++;
            else if(nums[i] == 2) swap(nums[i], nums[--right]);
        }
        return;
    }
};

struct ListNode
{
    int val;
    ListNode* next;
    ListNode(): val(0), next(nullptr) {}
    ListNode(int x): val(x), next(nullptr) {}
    ListNode(int x, ListNode* next): val(x), next(next) {}
}

class Solution 
{
public:
    void reorderList(ListNode* head) 
    {
        
    }
};


#include <iostream>
#include <string>

using namespace std;

int main()
{
    string str;
    cin >> str;

    int ret = INT_MAX;
    for(int i = 2; i <= str.size(); i++)
        for(int j = 0; j <= str.size() - i; j++)
        {
            string tmp = str.substr(j, i);
            if(isPalindromestring(tmp))
                ret = min(ret, i);
        }
    
    cout << (ret == INT_MAX ? -1 : ret) << endl;

    return 0;
}

bool isPalindromestring(string str)
{
    int l = 0, r = str.size() - 1;
    while(l != r)
        if(str[l++] != str[r--]) return false;
    return true;  
}

#include <iostream>
using namespace std;

const int N = 1e9;
int n, m;

int main()
{
    cin >> n >> m;
    int ret = m;
    for(int i = 0; i < n; i++)
        ret = ret * 
}

class Solution 
{
public:
    int lengthOfLIS(vector<int>& nums) 
    {
        int n = nums.size();

    }
};


class Solution 
{
public:
    int lengthOfLIS(vector<int>& nums) 
    {
        int n = nums.size(), max_len = 1;
        vector<int> dp(n, 1);
        int ret = 0;

        for(int i = 0; i < n; i++)
        {
            for(int j = 0; j < i; j++)
            {
                if(nums[i] > nums[j]) dp[i] = max(dp[i], dp[j] + 1);
            }
            max_len = max(max_len, dp[i]);
        }
        return max_len;
    }
};

// 优化方案：贪心 + 二分查找（O (n log n)）
// 上述动态规划的时间复杂度是 O(n²)，当 n 较大（如 n = 1e4）时会超时。更优的解法是 贪心 + 二分查找，时间复杂度降至 O(n log n)。
// 核心思路：
// 维护一个数组 tails，其中 tails[k] 表示「长度为 k+1 的 LIS 的最小末尾元素」。
// 遍历每个元素 num：
// 若 num 大于 tails 最后一个元素，说明可扩展最长子序列，将 num 加入 tails 末尾。
// 否则，在 tails 中二分查找第一个 ≥ num 的元素，用 num 替换它（使该长度的子序列末尾更小，后续更易扩展）。
// 最终 tails 的长度即为 LIS 的长度。

class Solution 
{
public:
    int lengthOfLIS(vector<int>& nums) 
    {
        int n = nums.size();
        if(n == 0) return 0; // 边界条件：空数组的LIS长度为0
        
        // tails数组：tails[i] 表示「长度为 i+1 的递增子序列的最小末尾元素」
        vector<int> tails; 

        // 遍历数组中的每个元素
        for(auto& x : nums)
        {
            auto it = lower_bound(tails.begin(), tails.end(), x);

            if(it == tails.end())
                tails.push_back(x);
            else 
                *it = x;
        }
        return tails.size();
    }
};

class Solution {
public:
    int lengthOfLIS(vector<int>& nums) {
        int n = nums.size();
        if (n == 0) return 0;  // 边界条件：空数组的LIS长度为0
        
        // tails数组：tails[i] 表示「长度为 i+1 的递增子序列的最小末尾元素」
        vector<int> tails; 

        // 遍历数组中的每个元素
        for (int num : nums) {
            // 二分查找：在tails中寻找第一个 >= num 的元素位置
            // lower_bound返回迭代器，指向第一个不小于num的元素
            auto it = lower_bound(tails.begin(), tails.end(), num);
            
            if (it == tails.end()) { 
                // 情况1：num比tails中所有元素都大
                // 说明可以形成更长的子序列（长度+1），将num加入tails末尾
                tails.push_back(num);
            } else { 
                // 情况2：找到一个位置it（tails[it] >= num）
                // 用num替换tails[it]，使该长度的子序列末尾元素更小
                // （后续元素更易大于该末尾，有机会形成更长序列）
                *it = num;
            }
        }

        // tails的长度就是最长递增子序列的长度
        return tails.size(); 
    }
};

// 要解决「乘积最大子数组」问题，核心难点在于负数的存在会反转乘积的大小关系（比如：当前最小乘积乘以负数，可能变成最大乘积）。因此，不能仅维护 “最大乘积”，还需同时维护 “最小乘积”。以下是详细思路：
// 思路核心：动态规划 + 同时维护最大 / 最小乘积
// 子数组是连续的，因此可以考虑「以每个位置为结尾的子数组的最大 / 最小乘积」，并通过动态规划推导状态。

// 状态定义
// prev_max：以当前元素的前一个元素结尾的子数组的最大乘积。
// prev_min：以当前元素的前一个元素结尾的子数组的最小乘积。
// （用两个变量替代数组，优化空间复杂度）

// 状态转移
// 对于当前元素 nums[i]，有三种可能的来源：
// 仅选当前元素（前面的乘积不如自己大 / 小）。
// 与前一个的最大乘积结合（prev_max * nums[i]）。
// 与前一个的最小乘积结合（prev_min * nums[i]，因为负数会让 “最小” 变 “最大”）。


class Solution 
{
public:
    int maxProduct(vector<int>& nums) 
    {
        int n = nums.size();

        int prev_max = nums[0];
        int prev_min = nums[0];
        int ret = nums[0];

        for(int i = 0; i < n; i++)
        {
            int cur_max = max({nums[i], prev_max * nums[i], prev_min * nums[i]});
            int cur_min = min({nums[i], prev_max * nums[i], prev_min * nums[i]});

            prev_max = cur_max;
            prev_min = cur_min;

            ret = max(ret, cur_max);
        }

        return ret;
    }
};

class Solution {
public:
    int maxProduct(vector<int>& nums) {
        int n = nums.size();
        if (n == 0) return 0;

        int prev_max = nums[0];   // 前一个位置的最大乘积
        int prev_min = nums[0];   // 前一个位置的最小乘积
        int result = nums[0];     // 全局最大乘积

        for (int i = 1; i < n; ++i) {
            // 计算当前的最大、最小乘积（三种情况取极值）
            int curr_max = max({nums[i], prev_max * nums[i], prev_min * nums[i]});
            int curr_min = min({nums[i], prev_max * nums[i], prev_min * nums[i]});

            // 更新前一个状态，供下一轮使用
            prev_max = curr_max;
            prev_min = curr_min;

            // 更新全局结果
            result = max(result, curr_max);
        }

        return result;
    }
};

class Solution 
{
public:
    bool canPartition(vector<int>& nums) 
    {
        int sum = 0, max_val = 0;
        for(auto& x : nums)
        {
            sum += x;
            max_val = max(max_val, x);
        }

        if(sum % 2 != 0) return false;
        int target = sum / 2;
        if(max_val > target) return false;

        vector<int> dp(target + 1, 0);

        for(int i = 0; i < n; i++)
        {
            for(int j = target; j >= nums[i]; j--)
            {
                dp[j] = max(dp[j], dp[j - nums[i]] + nums[i]);
            }
        }

        return dp[target] == target;
    }
};

// 判断数组能否分割为两个等和子集，等价于：数组总和必须为偶数（否则无法分成两个相等的整数和）。能否从数组中选出若干元素，使其和等于 总和的一半（target）。
// 这是典型的 0-1 背包问题：每个元素「选或不选」，目标是选出的元素和恰好为 target（可理解为 “背包容量为 target，物品重量和价值均为元素值，能否装满背包”）。
// 预处理优化：
// 总和为奇数时直接返回 false：因为两个相等的整数和必为偶数，奇数不可能被分为两个相等的整数。
// 最大元素 > target 时返回 false：若存在一个元素大于 target，它无法被任何子集包含（否则子集和会超过 target），因此不可能组成等和子集。
// 动态规划设计：
// dp[j] 的含义：考虑前 i 个元素后，能组成的不超过 j 的最大子集和。
// 初始状态 dp[0] = 0：和为 0 的子集一定存在（空集），其他位置初始为 0（尚未考虑任何元素）。
// 0-1 背包的倒序遍历：
// 内层循环从 target 倒序到 nums[i]，确保每个元素仅被考虑一次（避免重复选择，符合 0-1 背包 “每个物品只能选一次” 的约束）。
// 若正序遍历，会导致同一元素被多次累加（变成 “完全背包”，不符合题意）。
// 状态转移逻辑：
// 对于元素 nums[i] 和容量 j：
// 不选 nums[i]：dp[j] 保持不变（继承之前的最大和）。
// 选 nums[i]：则最大和为 dp[j - nums[i]] + nums[i]（即 “容量为 j - nums[i] 时的最大和” 加上当前元素值）。
// 取两者的最大值，确保 dp[j] 始终是当前能达到的最大和。
// 最终判断：
// 若 dp[target] == target，说明存在一个子集的和恰好为 target，即数组可分割为两个等和子集，返回 true；否则返回 false。

class Solution 
{
public:
    bool canPartition(vector<int>& nums) 
    {
        int sum = 0, max_val = 0;
        // 1. 计算数组总和sum和最大元素max_val
        for(auto& x : nums)
        {
            sum += x;          // 累加计算所有元素的总和
            max_val = max(max_val, x);  // 记录最大元素值
        }

        // 2. 预处理判断1：若总和为奇数，必然无法分割为两个等和子集（返回false）
        if(sum % 2 != 0) return false;
        
        // 3. 计算目标和target（总和的一半）
        int target = sum / 2;
        
        // 4. 预处理判断2：若最大元素 > target，无法分割（该元素无法被任何子集包含，否则和会超过target）
        if(max_val > target) return false;

        // 5. 动态规划数组：dp[j]表示「考虑前i个元素后，能组成的不超过j的最大子集和」
        // 目标是判断能否组成和为target的子集（即dp[target] == target）
        vector<int> dp(target + 1, 0);

        // 6. 遍历每个元素（0-1背包：每个元素只能选一次）
        for(int i = 0; i < nums.size(); i++)
        {
            // 7. 倒序遍历j（从target到nums[i]）：避免同一元素被重复选择（0-1背包核心）
            for(int j = target; j >= nums[i]; j--)
            {
                // 状态转移：对于当前元素nums[i]，有两种选择
                // - 不选：dp[j]保持不变
                // - 选：则最大和为「j - nums[i]时的最大和」+ nums[i]
                // 取两种选择的最大值
                dp[j] = max(dp[j], dp[j - nums[i]] + nums[i]);
            }
        }

        // 8. 若能组成和为target的子集，则返回true；否则false
        return dp[target] == target;
    }
};

class Solution 
{
public:
    vector<vector<int>> permute(vector<int>& nums) 
    {
        vector<vector<int>> ret;      
    }
};