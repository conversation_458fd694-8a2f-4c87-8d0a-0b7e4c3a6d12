class Solution 
{
public:
    int numSquares(int n) 
    {
        vector<int> dp(n + 1, INT_MAX);
        dp[0] = 0; // 初始化

        for(int i = 1; i <= n; i++)
            for(int j = 1; j * j <= i; j++)
                dp[i] = min(dp[i], dp[i - j * j] + 1);

        return dp[n];
    }
};

class Solution 
{
public:
    int majorityElement(vector<int>& nums) 
    {
        int n = nums.size();
        unordered_map<int, int> cnt;
        for(auto& x : nums)
            if(++cnt[x] > n / 2) return x;
        return -1;
    }
};
// 进阶：尝试设计时间复杂度为 O(n)、空间复杂度为 O(1) 的算法解决此问题。