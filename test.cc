#include <iostream>
#include <vector>
using namespace std;

class Solution 
{
public:
    int numSquares(int n) 
    {
        vector<int> dp(n + 1, INT_MAX);
        dp[0] = 0; // 初始化

        for(int i = 1; i <= n; i++)
            for(int j = 1; j * j <= i; j++)
                dp[i] = min(dp[i], dp[i - j * j] + 1);

        return dp[n];
    }
};

class Solution 
{
public:
    int majorityElement(vector<int>& nums) 
    {
        int n = nums.size();
        unordered_map<int, int> cnt;
        for(auto& x : nums)
            if(++cnt[x] > n / 2) return x;
        return -1;
    }
};
// 进阶：尝试设计时间复杂度为 O(n)、空间复杂度为 O(1) 的算法解决此问题。

class Solution 
{
public:
    int majorityElement(vector<int>& nums) 
    {
        int candidate = nums[0], cnt = 1;
        for(int i = 1; i < nums.size(); i++)
        {
            if(nums[i] == candidate) cnt++;
            else
            {
                cnt--;
                if(cnt == 0) 
                {
                    candidate = nums[i];
                    cnt = 1;
                }
            }
        }
        return candidate;
    }
};


class Solution 
{
public:
    void sortColors(vector<int>& nums) 
    {
        int left = -1, right = nums.size(), i = 0;
        while(i < nums.size())
        {
            if(nums[i] == 0) swap(nums[i++], nums[++left]);
            else if(nums[i] == 1) i++;
            else if(nums[i] == 2) swap(nums[i], nums[--right]);
        }
        return;
    }
};

struct ListNode
{
    int val;
    ListNode* next;
    ListNode(): val(0), next(nullptr) {}
    ListNode(int x): val(x), next(nullptr) {}
    ListNode(int x, ListNode* next): val(x), next(next) {}
}

class Solution 
{
public:
    void reorderList(ListNode* head) 
    {
        
    }
};


#include <iostream>
#include <string>

using namespace std;

int main()
{
    string str;
    cin >> str;

    int ret = INT_MAX;
    for(int i = 2; i <= str.size(); i++)
        for(int j = 0; j <= str.size() - i; j++)
        {
            string tmp = str.substr(j, i);
            if(isPalindromestring(tmp))
                ret = min(ret, i);
        }
    
    cout << (ret == INT_MAX ? -1 : ret) << endl;

    return 0;
}

bool isPalindromestring(string str)
{
    int l = 0, r = str.size() - 1;
    while(l != r)
        if(str[l++] != str[r--]) return false;
    return true;  
}

#include <iostream>
using namespace std;

const int N = 1e9;
int n, m;

int main()
{
    cin >> n >> m;
    int ret = m;
    for(int i = 0; i < n; i++)
        ret = ret * 
}

class Solution 
{
public:
    int lengthOfLIS(vector<int>& nums) 
    {
        int n = nums.size();

    }
};


class Solution 
{
public:
    int lengthOfLIS(vector<int>& nums) 
    {
        int n = nums.size(), max_len = 1;
        vector<int> dp(n, 1);
        int ret = 0;

        for(int i = 0; i < n; i++)
        {
            for(int j = 0; j < i; j++)
            {
                if(nums[i] > nums[j]) dp[i] = max(dp[i], dp[j] + 1);
            }
            max_len = max(max_len, dp[i]);
        }
        return max_len;
    }
};

// 优化方案：贪心 + 二分查找（O (n log n)）
// 上述动态规划的时间复杂度是 O(n²)，当 n 较大（如 n = 1e4）时会超时。更优的解法是 贪心 + 二分查找，时间复杂度降至 O(n log n)。
// 核心思路：
// 维护一个数组 tails，其中 tails[k] 表示「长度为 k+1 的 LIS 的最小末尾元素」。
// 遍历每个元素 num：
// 若 num 大于 tails 最后一个元素，说明可扩展最长子序列，将 num 加入 tails 末尾。
// 否则，在 tails 中二分查找第一个 ≥ num 的元素，用 num 替换它（使该长度的子序列末尾更小，后续更易扩展）。
// 最终 tails 的长度即为 LIS 的长度。

class Solution 
{
public:
    int lengthOfLIS(vector<int>& nums) 
    {
        int n = nums.size();
        if(n == 0) return 0; // 边界条件：空数组的LIS长度为0
        
        // tails数组：tails[i] 表示「长度为 i+1 的递增子序列的最小末尾元素」
        vector<int> tails; 

        // 遍历数组中的每个元素
        for(auto& x : nums)
        {
            auto it = lower_bound(tails.begin(), tails.end(), x);

            if(it == tails.end())
                tails.push_back(x);
            else 
                *it = x;
        }
        return tails.size();
    }
};

class Solution {
public:
    int lengthOfLIS(vector<int>& nums) {
        int n = nums.size();
        if (n == 0) return 0;  // 边界条件：空数组的LIS长度为0
        
        // tails数组：tails[i] 表示「长度为 i+1 的递增子序列的最小末尾元素」
        vector<int> tails; 

        // 遍历数组中的每个元素
        for (int num : nums) {
            // 二分查找：在tails中寻找第一个 >= num 的元素位置
            // lower_bound返回迭代器，指向第一个不小于num的元素
            auto it = lower_bound(tails.begin(), tails.end(), num);
            
            if (it == tails.end()) { 
                // 情况1：num比tails中所有元素都大
                // 说明可以形成更长的子序列（长度+1），将num加入tails末尾
                tails.push_back(num);
            } else { 
                // 情况2：找到一个位置it（tails[it] >= num）
                // 用num替换tails[it]，使该长度的子序列末尾元素更小
                // （后续元素更易大于该末尾，有机会形成更长序列）
                *it = num;
            }
        }

        // tails的长度就是最长递增子序列的长度
        return tails.size(); 
    }
};

// 要解决「乘积最大子数组」问题，核心难点在于负数的存在会反转乘积的大小关系（比如：当前最小乘积乘以负数，可能变成最大乘积）。因此，不能仅维护 “最大乘积”，还需同时维护 “最小乘积”。以下是详细思路：
// 思路核心：动态规划 + 同时维护最大 / 最小乘积
// 子数组是连续的，因此可以考虑「以每个位置为结尾的子数组的最大 / 最小乘积」，并通过动态规划推导状态。

// 状态定义
// prev_max：以当前元素的前一个元素结尾的子数组的最大乘积。
// prev_min：以当前元素的前一个元素结尾的子数组的最小乘积。
// （用两个变量替代数组，优化空间复杂度）

// 状态转移
// 对于当前元素 nums[i]，有三种可能的来源：
// 仅选当前元素（前面的乘积不如自己大 / 小）。
// 与前一个的最大乘积结合（prev_max * nums[i]）。
// 与前一个的最小乘积结合（prev_min * nums[i]，因为负数会让 “最小” 变 “最大”）。


class Solution 
{
public:
    int maxProduct(vector<int>& nums) 
    {
        int n = nums.size();

        int prev_max = nums[0];
        int prev_min = nums[0];
        int ret = nums[0];

        for(int i = 0; i < n; i++)
        {
            int cur_max = max({nums[i], prev_max * nums[i], prev_min * nums[i]});
            int cur_min = min({nums[i], prev_max * nums[i], prev_min * nums[i]});

            prev_max = cur_max;
            prev_min = cur_min;

            ret = max(ret, cur_max);
        }

        return ret;
    }
};
