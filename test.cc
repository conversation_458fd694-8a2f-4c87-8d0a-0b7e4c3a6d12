#include <iostream>
#include <vector>
using namespace std;

class Solution 
{
public:
    int numSquares(int n) 
    {
        vector<int> dp(n + 1, INT_MAX);
        dp[0] = 0; // 初始化

        for(int i = 1; i <= n; i++)
            for(int j = 1; j * j <= i; j++)
                dp[i] = min(dp[i], dp[i - j * j] + 1);

        return dp[n];
    }
};

class Solution 
{
public:
    int majorityElement(vector<int>& nums) 
    {
        int n = nums.size();
        unordered_map<int, int> cnt;
        for(auto& x : nums)
            if(++cnt[x] > n / 2) return x;
        return -1;
    }
};
// 进阶：尝试设计时间复杂度为 O(n)、空间复杂度为 O(1) 的算法解决此问题。

class Solution 
{
public:
    int majorityElement(vector<int>& nums) 
    {
        int candidate = nums[0], cnt = 1;
        for(int i = 1; i < nums.size(); i++)
        {
            if(nums[i] == candidate) cnt++;
            else
            {
                cnt--;
                if(cnt == 0) 
                {
                    candidate = nums[i];
                    cnt = 1;
                }
            }
        }
        return candidate;
    }
};


class Solution 
{
public:
    void sortColors(vector<int>& nums) 
    {
        int left = -1, right = nums.size(), i = 0;
        while(i < nums.size())
        {
            if(nums[i] == 0) swap(nums[i++], nums[++left]);
            else if(nums[i] == 1) i++;
            else if(nums[i] == 2) swap(nums[i], nums[--right]);
        }
        return;
    }
};

struct ListNode
{
    int val;
    ListNode* next;
    ListNode(): val(0), next(nullptr) {}
    ListNode(int x): val(x), next(nullptr) {}
    ListNode(int x, ListNode* next): val(x), next(next) {}
}

class Solution 
{
public:
    void reorderList(ListNode* head) 
    {
        
    }
};