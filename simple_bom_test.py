#!/usr/bin/python3
"""
简化的BOM测试脚本
使用最常见的字段名进行测试
"""

import datetime
import json
import config
import vehicle_sw_version_table
from log_config import logger


def test_common_field_names():
    """测试常见的字段名组合"""
    
    print("=" * 80)
    print("测试常见字段名组合")
    print("=" * 80)
    
    # 常见字段名的几种可能组合
    field_name_sets = [
        # 组合1: 基于截图中看到的字段名
        {
            "vin": "vin",
            "part_number": "part_number",
            "en_name": "en_name",
            "cn_name": "cn_name",
            "quantity": "quantity",
            "uom": "uom"
        },

        # 组合2: 完整字段名（如果表格有更多列）
        {
            "vin": "vin",
            "part_number": "part_number",
            "part_revision": "part_revision",
            "en_name": "en_name",
            "cn_name": "cn_name",
            "quantity": "quantity",
            "uom": "uom",
            "partition_code": "partition_code",
            "creator": "creator",
            "create_time": "create_time",
            "modifier": "modifier",
            "modify_time": "modify_time",
            "status": "status"
        },
        
        # 组合2: 使用中文字段名
        {
            "vin": "车辆识别码",
            "part_number": "零件号",
            "part_revision": "零件版本", 
            "en_name": "英文名称",
            "cn_name": "中文名称",
            "quantity": "数量",
            "uom": "单位",
            "partition_code": "分区代码",
            "creator": "创建者",
            "create_time": "创建时间",
            "modifier": "修改者",
            "modify_time": "修改时间",
            "status": "状态"
        },
        
        # 组合3: 混合命名
        {
            "vin": "VIN",
            "part_number": "partNumber",
            "part_revision": "partRevision",
            "en_name": "enName", 
            "cn_name": "cnName",
            "quantity": "qty",
            "uom": "UOM",
            "partition_code": "partitionCode",
            "creator": "creator",
            "create_time": "createTime",
            "modifier": "modifier",
            "modify_time": "modifyTime",
            "status": "status"
        }
    ]
    
    # 测试数据
    test_data = {
        "vin": "TEST123456",
        "part_number": "P0373183",
        "part_revision": "AC", 
        "en_name": "ANTENNA-GNSS MAIN",
        "cn_name": "GNSS 主天线",
        "quantity": "1",
        "uom": "EA",
        "partition_code": "ELE.10.04.05",
        "creator": "VINBOM",
        "create_time": "2025-08-08 18:08:04",
        "modifier": "VINBOM",
        "modify_time": "2025-08-08 18:08:04", 
        "status": "Assembled"
    }
    
    bom_table = vehicle_sw_version_table.VehicleBom()
    
    for i, field_mapping in enumerate(field_name_sets, 1):
        print(f"\n🧪 测试组合 {i}:")
        
        # 构建测试记录
        test_record = {}
        for original_field, mapped_field in field_mapping.items():
            test_record[mapped_field] = test_data[original_field]
        
        print(f"字段映射: {json.dumps(field_mapping, ensure_ascii=False, indent=2)}")
        
        # 尝试插入
        try:
            success = bom_table.create_record(test_record)
            if success:
                print(f"✅ 组合 {i} 插入成功！")
                print("🎉 找到正确的字段映射:")
                
                # 保存成功的映射
                save_working_mapping(field_mapping)
                
                # 清理测试数据
                cleanup_test_data(bom_table, test_record)
                return field_mapping
            else:
                print(f"❌ 组合 {i} 插入失败")
        except Exception as e:
            print(f"❌ 组合 {i} 插入异常: {str(e)}")
    
    print("\n❌ 所有常见字段名组合都失败了")
    return None


def save_working_mapping(field_mapping):
    """保存有效的字段映射"""
    
    mapping_code = f'''#!/usr/bin/python3
"""
BOM字段映射配置
自动生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

def get_bom_field_mapping():
    """获取BOM字段映射配置"""
    return {json.dumps(field_mapping, ensure_ascii=False, indent=8)[1:-1]}

def convert_bom_record(tvas_record, vin):
    """转换BOM记录格式"""
    import datetime
    
    mapping = get_bom_field_mapping()
    
    # 处理时间字段
    create_time_str = ""
    modify_time_str = ""
    
    if tvas_record.get("create_time"):
        try:
            dt = datetime.datetime.fromtimestamp(int(tvas_record["create_time"]))
            create_time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            create_time_str = str(tvas_record["create_time"])
    
    if tvas_record.get("modify_time"):
        try:
            dt = datetime.datetime.fromtimestamp(int(tvas_record["modify_time"]))
            modify_time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            modify_time_str = str(tvas_record["modify_time"])
    
    # 构建格式化记录
    formatted_record = {{}}
    
    for tvas_field, feishu_field in mapping.items():
        if tvas_field == "vin":
            formatted_record[feishu_field] = vin
        elif tvas_field == "create_time":
            formatted_record[feishu_field] = create_time_str
        elif tvas_field == "modify_time":
            formatted_record[feishu_field] = modify_time_str
        else:
            value = tvas_record.get(tvas_field, "")
            formatted_record[feishu_field] = str(value) if value is not None else ""
    
    return formatted_record
'''
    
    with open("bom_field_mapping.py", "w", encoding="utf-8") as f:
        f.write(mapping_code)
    
    print(f"✅ 字段映射已保存到: bom_field_mapping.py")


def cleanup_test_data(bom_table, test_record):
    """清理测试数据"""
    try:
        # 查找并删除测试记录
        for record in bom_table.get_all_records():
            fields = record.get("fields", {})
            # 检查是否是测试记录（通过VIN识别）
            for field_name, field_value in fields.items():
                if isinstance(field_value, list) and len(field_value) > 0:
                    if field_value[0].get("text") == "TEST123456":
                        bom_table.delete_record(record.get("record_id"))
                        print("🧹 已清理测试数据")
                        return
                elif field_value == "TEST123456":
                    bom_table.delete_record(record.get("record_id"))
                    print("🧹 已清理测试数据")
                    return
    except Exception as e:
        print(f"清理测试数据时出错: {e}")


def manual_field_input():
    """手动输入字段映射"""
    
    print("\n" + "=" * 80)
    print("手动字段映射")
    print("=" * 80)
    
    print("请打开飞书BOM表格查看实际的列名:")
    print("https://nio.feishu.cn/base/XuG3bU8uDaR4Nts7geLcTO1Jnhh?table=tblvLdUKy6MRZXPX&view=vewI62gWSb")
    
    input("\n按回车键继续...")
    
    fields = [
        ("vin", "车辆识别码"),
        ("part_number", "零件号"), 
        ("part_revision", "零件版本"),
        ("en_name", "英文名称"),
        ("cn_name", "中文名称"),
        ("quantity", "数量"),
        ("uom", "单位"),
        ("partition_code", "分区代码"),
        ("creator", "创建者"),
        ("create_time", "创建时间"),
        ("modifier", "修改者"),
        ("modify_time", "修改时间"),
        ("status", "状态")
    ]
    
    field_mapping = {}
    
    print("\\n请输入飞书表格中对应的列名 (如果没有该列，输入 'skip'):")
    
    for field_key, field_desc in fields:
        while True:
            feishu_name = input(f"{field_desc:10} ({field_key:15}): ").strip()
            if feishu_name.lower() == 'skip':
                print(f"跳过字段: {field_desc}")
                break
            elif feishu_name:
                field_mapping[field_key] = feishu_name
                break
            else:
                print("请输入有效的列名或 'skip'")
    
    if field_mapping:
        print(f"\\n✅ 完成映射，共 {len(field_mapping)} 个字段")
        save_working_mapping(field_mapping)
        return field_mapping
    else:
        print("\\n❌ 未设置任何字段映射")
        return None


def main():
    """主函数"""
    
    print("BOM字段映射快速测试工具")
    print("=" * 80)
    
    print("\\n选择测试方式:")
    print("1. 自动测试常见字段名组合")
    print("2. 手动输入字段映射")
    
    choice = input("\\n请选择 (1/2): ").strip()
    
    if choice == "1":
        print("\\n开始自动测试...")
        mapping = test_common_field_names()
        if mapping:
            print("\\n🎉 自动测试成功！可以使用生成的 bom_field_mapping.py")
        else:
            print("\\n❌ 自动测试失败，建议使用手动输入方式")
            
    elif choice == "2":
        print("\\n开始手动输入...")
        mapping = manual_field_input()
        if mapping:
            print("\\n✅ 手动映射完成！")
        
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
