import json
import threading

import requests
from typing import Optional

# 导入配置文件（包含APP_ID和APP_SECRET等信息）
import config
# 导入日志配置（用于记录操作日志）
from log_config import logger


class GetAccessToken:
    """飞书访问令牌获取类，用于获取租户访问令牌(tenant_access_token)"""
    
    def __init__(self):
        """初始化访问令牌获取对象，准备请求参数"""
        # 构建请求体，包含从配置文件获取的APP_ID和APP_SECRET
        self.payload = json.dumps({
            "app_id": config.APP_ID,
            "app_secret": config.APP_SECRET,
        })

    def get_tenant_access_token(self) -> Optional[str]:
        """
        获取飞书租户访问令牌
        
        返回:
            有效的访问令牌字符串，如果获取失败则返回None
        """
        # 飞书获取租户访问令牌的API地址
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        headers = {
            'Content-Type': 'application/json'
        }
        
        # 发送POST请求获取令牌
        response = requests.request("POST", url, headers=headers, data=self.payload)

        # 检查响应状态
        if response.ok and response.status_code == 200:
            response_dict = response.json()
            # 飞书API成功返回code=0且msg="ok"
            if response_dict.get("code") == 0 and response_dict.get("msg") == "ok":
                tenant_access_token = response_dict.get("tenant_access_token")
                if tenant_access_token:
                    return tenant_access_token

            # 打印错误信息
            logger.error(response_dict)
            return None

        logger.error(f"Failed to get tenant_access_token!")
        logger.error(response.text)
        return None


def timeout(seconds):
    """
    超时装饰器，用于限制函数执行时间
    
    参数:
        seconds: 超时时间（秒）
        
    返回:
        装饰器函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs) -> Optional[dict]:
            result = None  # 用于存储函数执行结果
            
            # 定义内部函数，用于在子线程中执行目标函数
            def new_func():
                nonlocal result
                result = func(*args, **kwargs)

            # 创建并启动子线程
            t = threading.Thread(target=new_func)
            t.start()
            # 等待指定时间，超过则视为超时
            t.join(seconds)
            
            # 如果线程仍在运行，说明超时
            if t.is_alive():
                raise TimeoutError(Exception(f"Function timed out after {seconds} seconds"))

            return result

        return wrapper

    return decorator


def response_checker(response, operation: str, msg="success") -> tuple:
    """
    检查API响应结果是否成功
    
    参数:
        response: API响应对象
        operation: 操作描述（用于日志）
        msg: 预期的成功消息
        
    返回:
        元组 (success: bool, need_retry: bool)
        - success: 操作是否成功
        - need_retry: 是否需要刷新令牌并重试
    """
    # 检查HTTP响应状态
    if response.ok and response.status_code == 200:
        response_dict = response.json()
        # 检查飞书API返回的状态码和消息
        if response_dict.get("code") == 0 and response_dict.get("msg") == msg:
            logger.debug(f"{operation} success.")
            return True, False
        
        # 解析错误信息
        error_code = response_dict.get("code")
        error_msg = response_dict.get("msg", "")

        # 检查是否为访问令牌过期错误（99991663是飞书令牌过期的错误码）
        if error_code == 99991663:
            logger.warning(f"{operation} failed: Access token expired - {error_msg}")
            return False, True  # 需要重试
        
        # 检查是否为"记录未找到"错误
        elif error_code == 1254043 and "record not found" in error_msg:
            logger.warning(f"{operation} failed: Record not found - {error_msg}")
            return False, False  # 不需要重试
        
        # 其他错误
        else:
            logger.error(f"{operation} failed: {response_dict}")
            return False, False

    # HTTP请求失败
    logger.error(f"{operation} failed: {response.text}")
    return False, False


def split_list(lst, chunk_size=500):
    """
    将列表分割为指定大小的子列表（用于批量操作）
    
    参数:
        lst: 要分割的列表
        chunk_size: 每个子列表的最大长度
        
    返回:
        子列表组成的列表
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


class LarkRequests:
    """飞书多维表格(Bitable)请求类，提供与飞书表格交互的各种方法"""
    
    def __init__(self, app, table):
        """
        初始化飞书表格请求对象
        
        参数:
            app: 飞书应用ID
            table: 表格ID
        """
        self.app = app                  # 飞书应用ID
        self.table = table              # 表格ID
        self.get_access_token = GetAccessToken()  # 访问令牌获取对象

        self.method = ""                # HTTP方法（GET, POST, PUT等）
        self.url = ""                   # 请求URL
        # 初始化请求头，指定内容类型为JSON
        self.headers = {
            "Content-Type": "application/json",
        }
        self.payload = None             # 请求体
        self._update_auth_header()      # 更新授权头部信息

    def _update_auth_header(self):
        """更新请求头中的授权信息（添加访问令牌）"""
        token = self.get_access_token.get_tenant_access_token()
        if token:
            self.headers["Authorization"] = "Bearer " + token
        else:
            logger.error("Failed to get access token")

    def _refresh_token_and_retry(self, func, *args, **kwargs):
        """
        刷新令牌并重试请求
        
        参数:
            func: 要重试的请求函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        返回:
            函数执行结果
        """
        logger.warning("Access token may be expired, refreshing...")
        self._update_auth_header()
        return func(*args, **kwargs)

    @timeout(10)
    def get_records_handle(self, filter_dict, page_token) -> Optional[list]:
        """
        处理获取记录的请求（带超时控制）
        
        参数:
            filter_dict: 过滤条件字典
            page_token: 分页令牌，用于获取下一页数据
            
        返回:
            包含记录数据的字典，如果失败则返回None
        """
        info = f"Get records from the lark with page_token {page_token}"
        logger.debug(f"{info}...")
        self.method = "POST"
        self.payload = json.dumps(filter_dict)

        # 构建请求URL，带分页参数
        if page_token:
            self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                       f"/tables/{self.table}/records/search?page_size=100&page_token={page_token}"
        else:
            self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                       f"/tables/{self.table}/records/search?page_size=100"

        # 发送请求
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        
        # 处理响应
        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("code") == 0 and response_dict.get("msg") == "success":
                response_data = response_dict.get("data", {})
                logger.debug(f"{info} success.")
                return response_data
            # 处理令牌过期情况
            elif response_dict.get("code") == 99991663:
                logger.warning(f"{info} failed: Access token expired, refreshing...")
                self._update_auth_header()
                # 刷新令牌后重试
                response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
                if response.ok and response.status_code == 200:
                    response_dict = response.json()
                    if response_dict.get("code") == 0 and response_dict.get("msg") == "success":
                        response_data = response_dict.get("data", {})
                        logger.debug(f"{info} success after token refresh.")
                        return response_data
                logger.error(response_dict)
                return None
            else:
                logger.error(response_dict)
                return None

        logger.error(response.text)
        return None

    def get_records(self, filter_dict=None, page_token=None) -> Optional[dict]:
        """
        获取表格记录（封装了超时处理）
        
        参数:
            filter_dict: 过滤条件字典
            page_token: 分页令牌
            
        返回:
            包含记录数据的字典，如果超时或失败则返回None
        """
        if filter_dict is None:
            filter_dict = {}
        try:
            res = self.get_records_handle(filter_dict, page_token)
        except TimeoutError as e:
            logger.error(e)
            return None
        else:
            return res

    def get_all_records(self, filter_dict=None) -> list:
        """
        获取表格中所有符合条件的记录（自动处理分页）
        
        参数:
            filter_dict: 过滤条件字典
            
        返回:
            所有记录组成的列表
        """
        if filter_dict is None:
            filter_dict = {}
        has_more = True          # 是否还有更多数据
        page_token = None        # 分页令牌
        total = 0                # 总记录数
        total_item = []          # 存储所有记录
        
        # 循环获取所有分页数据
        while has_more:
            logger.debug(f"At page_token: {page_token}")
            response_data = self.get_records(filter_dict, page_token)
            if response_data:
                record_items = response_data.get("items")
                has_more = response_data.get("has_more", False)
                page_token = response_data.get("page_token")
                if record_items:
                    total_item += record_items
                    total = response_data.get("total")
                else:
                    logger.warning(f"Records item is empty at page_token {page_token}!")
            else:
                logger.error(f"Failed to get records at page_token {page_token}!")

        # 验证获取的记录数是否与总记录数一致
        if len(total_item) == total:
            logger.debug(f"Get all {total} records from lark.")
        else:
            logger.error(f"Get {len(total_item)} records from lark != total {total}!")

        return total_item

    def create_record(self, record_dict: dict) -> bool:
        """
        创建单条记录
        
        参数:
            record_dict: 包含字段和值的字典
            
        返回:
            布尔值：True表示创建成功，False表示失败
        """
        info = "Create record to the lark"
        logger.debug(f"{info}...")
        self.method = "POST"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records"
        self.payload = json.dumps({
            "fields": record_dict
        })

        # 发送请求
        res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        success, need_retry = response_checker(res, info)

        # 如果失败且需要重试（令牌过期），刷新令牌后重试
        if not success and need_retry:
            self._update_auth_header()
            res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
            success, _ = response_checker(res, info)

        return success

    def update_record(self, record_dict: dict, record_id) -> bool:
        """
        更新单条记录
        
        参数:
            record_dict: 包含要更新的字段和值的字典
            record_id: 要更新的记录ID
            
        返回:
            布尔值：True表示更新成功，False表示失败
        """
        info = f"Update record {record_id} to the lark"
        logger.debug(f"{info}...")
        self.method = "PUT"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/{record_id}"
        self.payload = json.dumps({
            "fields": record_dict
        })

        # 发送请求
        res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        success, need_retry = response_checker(res, info)

        # 如果失败且需要重试（令牌过期），刷新令牌后重试
        if not success and need_retry:
            self._update_auth_header()
            res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
            success, _ = response_checker(res, info)

        # 处理记录未找到的情况
        if not success:
            if res.ok and res.status_code == 200:
                response_dict = res.json()
                error_code = response_dict.get("code")
                if error_code == 1254043:  # 记录未找到错误码
                    logger.warning(f"Record {record_id} not found, it may have been deleted")
        return success

    def delete_record(self, record_id) -> bool:
        """
        删除单条记录
        
        参数:
            record_id: 要删除的记录ID
            
        返回:
            布尔值：True表示删除成功，False表示失败
        """
        info = "Delete record from the lark"
        logger.debug(f"{info}...")
        self.method = "DELETE"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/{record_id}"
        self.payload = ""

        # 发送请求
        res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        success, need_retry = response_checker(res, info)

        # 如果失败且需要重试（令牌过期），刷新令牌后重试
        if not success and need_retry:
            self._update_auth_header()
            res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
            success, _ = response_checker(res, info)

        return success

    def _create_batch(self, batch: list) -> bool:
        """
        执行单个批次的创建操作（内部使用）
        
        参数:
            batch: 一个包含多条记录的列表
            
        返回:
            布尔值：True表示批次创建成功，False表示失败
        """
        info = f"Batch create {len(batch)} records to the lark"
        logger.debug(f"{info}...")

        self.method = "POST"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/batch_create"

        self.payload = json.dumps({
            "records": batch
        })

        # 发送请求
        res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        success, need_retry = response_checker(res, info)

        # 如果失败且需要重试（令牌过期），刷新令牌后重试
        if not success and need_retry:
            self._update_auth_header()
            res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
            success, _ = response_checker(res, info)

        return success

    def batch_create(self, records: list) -> bool:
        """
        批量创建记录
        
        参数:
            records: 包含多条记录的列表，每条记录是一个字段字典
            
        返回:
            布尔值：True表示所有记录创建成功，False表示有失败
        """
        if not records:
            return True

        # 调试：打印第一条记录的格式和记录总数
        if records:
            logger.debug(f"批量创建记录格式检查 - 第一条记录: {records[0]}")
            logger.debug(f"记录总数: {len(records)}")

        # 确保每条记录都有正确的格式（包含"fields"键）
        formatted_records = []
        for record in records:
            if isinstance(record, dict):
                if "fields" in record:
                    formatted_records.append(record)
                else:
                    # 如果没有fields包装，添加包装
                    formatted_records.append({"fields": record})
            else:
                logger.error(f"无效的记录格式: {type(record)} - {record}")
                continue

        # 分批处理，每批最多500条记录
        batch_size = 500
        success_count = 0

        # 遍历所有批次
        for i in range(0, len(formatted_records), batch_size):
            batch = formatted_records[i:i + batch_size]
            if self._create_batch(batch):
                success_count += len(batch)
            else:
                logger.warning(f"Failed to create batch of {len(batch)} records")

        # 检查是否所有记录都创建成功
        return success_count == len(formatted_records)

    def batch_update(self, records: list) -> bool:
        """
        批量更新记录
        
        参数:
            records: 包含多条记录的列表，每条记录应包含"record_id"和"fields"键
                    示例: [{"record_id": "xxx", "fields": {"字段1": "值1"}}]
            
        返回:
            布尔值：True表示至少有部分记录更新成功，False表示全部失败
        """
        info = "Batch update records to the lark"
        logger.debug(f"{info}...")
        if len(records) == 0:
            logger.warning("Records is empty, don't need to update!")
            return True

        self.method = "POST"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/batch_update"

        # 将记录列表分割为多个子列表（每批最多500条）
        sub_lists = split_list(records)
        success_count = 0
        total_count = len(records)

        # 处理每个子列表
        for sub_list in sub_lists:
            self.payload = json.dumps({
                "records": sub_list
            })

            # 发送请求
            res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
            success, need_retry = response_checker(res, info)

            # 如果失败且需要重试（令牌过期），刷新令牌后重试
            if not success and need_retry:
                self._update_auth_header()
                res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
                success, _ = response_checker(res, info)

            if success:
                success_count += len(sub_list)
            else:
                # 如果批量更新失败，尝试逐条更新以定位问题记录
                logger.warning(f"Batch update failed for {len(sub_list)} records, trying individual updates...")
                for record in sub_list:
                    record_id = record.get("record_id")
                    fields = record.get("fields", {})
                    if self.update_record(fields, record_id):
                        success_count += 1
                    else:
                        logger.warning(f"Failed to update record {record_id}, it may have been deleted")

        logger.info(f"Successfully updated {success_count}/{total_count} records")
        # 如果至少有一条记录更新成功，返回True
        return success_count > 0

    def batch_delete(self, record_ids: list) -> bool:
        """
        批量删除记录
        
        参数:
            record_ids: 要删除的记录ID列表
            
        返回:
            布尔值：True表示所有批次删除成功，False表示有失败
        """
        info = "Batch delete records from the lark"
        logger.debug(f"{info}...")
        if len(record_ids) == 0:
            logger.warning("Records is empty, don't need to delete!")
            return True

        self.method = "POST"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/batch_delete"

        # 将记录ID列表分割为多个子列表（每批最多500条）
        sub_lists = split_list(record_ids)
        for sub_list in sub_lists:
            self.payload = json.dumps({
                "records": sub_list
            })

            # 发送请求
            res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
            success, need_retry = response_checker(res, info)

            # 如果失败且需要重试（令牌过期），刷新令牌后重试
            if not success and need_retry:
                self._update_auth_header()
                res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
                success, _ = response_checker(res, info)

            if not success:
                return False

        return True

    def get_table_fields(self) -> Optional[dict]:
        """
        获取表格的字段信息（字段定义）
        
        返回:
            包含字段信息的字典，如果失败则返回None
        """
        info = "Get table fields from lark"
        logger.debug(f"{info}...")

        self.method = "GET"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}/tables/{self.table}/fields"

        # 发送请求
        res = requests.request(self.method, self.url, headers=self.headers)
        success, need_retry = response_checker(res, info)

        # 如果失败且需要重试（令牌过期），刷新令牌后重试
        if not success and need_retry:
            self._update_auth_header()
            res = requests.request(self.method, self.url, headers=self.headers)
            success, _ = response_checker(res, info)

        if success:
            response_dict = res.json()
            fields_data = response_dict.get("data", {})
            return fields_data

        return None
    
class VehicleBom:
    def __init__(self):
        """
        初始化飞书BOM表格操作对象·
        - app: 多维表格的App Token（从 config.APP_TOKEN 获取）
        - table: BOM表格的Table ID（从 config.BOM_TABLE_ID 获取）
        """
        self.lark_client = LarkRequests(
            app=config.APP_TOKEN,   # 关键修正：用多维表格的App Token
            table=config.BOM_TABLE_ID  
        )

    def delete_all_bom_records(self) -> bool:
        """
        删除表格中所有BOM记录（分两步：先获取所有记录ID，再批量删除）
        :return: 全部删除成功返回True，否则返回False
        """
        try:
            logger.info("开始删除BOM表格中所有记录...")
            
            # Step 1: 获取所有记录的ID（自动处理分页）
            all_records = self.lark_client.get_all_records()
            if not all_records:
                logger.info("BOM表格中无记录，无需删除")
                return True  # 无数据也算“删除成功”
            
            # 提取所有记录的ID（防御性处理：确保record_id存在）
            record_ids = [
                record.get("record_id") 
                for record in all_records 
                if record.get("record_id")  # 过滤无效ID
            ]
            if not record_ids:
                logger.warning("未提取到有效记录ID，无法执行删除")
                return False
            
            logger.info(f"共获取到 {len(record_ids)} 条BOM记录ID，准备批量删除")
            
            # Step 2: 批量删除记录（自动分批，每批最多500条）
            delete_success = self.lark_client.batch_delete(record_ids)
            
            if delete_success:
                logger.info("所有BOM记录已成功删除")
            else:
                logger.error("BOM记录批量删除失败（可能部分或全部失败）")
            
            return delete_success
        
        except Exception as e:
            logger.error(f"删除所有BOM记录时发生异常: {str(e)}")
            return False
    
    def batch_create(self, records: list) -> bool:
        """
        批量创建BOM记录（直接代理LarkRequests的batch_create方法）
        :param records: 要创建的记录列表（格式：[{"fields": {...}}] 或 [{...}]）
        :return: 是否全部创建成功
        """
        return self.lark_client.batch_create(records)