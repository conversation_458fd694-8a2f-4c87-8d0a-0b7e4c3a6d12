#!/usr/bin/python3
"""
修复版本的BOM数据同步脚本
解决时间戳格式和字段映射问题
"""

import datetime
import json
import config
import login_nio
import get_tvas_info
import vehicle_sw_version_table
from log_config import logger


def format_timestamp_to_string(timestamp):
    """
    将时间戳转换为格式化字符串
    :param timestamp: 时间戳（秒级）
    :return: 格式化的时间字符串
    """
    if not timestamp:
        return ""
    
    try:
        dt = datetime.datetime.fromtimestamp(int(timestamp))
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except (ValueError, TypeError) as e:
        logger.warning(f"时间戳转换失败: {timestamp}, 错误: {e}")
        return str(timestamp)


def get_safe_field_mapping():
    """
    获取安全的字段映射配置
    根据飞书表格的实际字段名进行映射
    """
    # 这个映射需要根据实际的飞书表格字段名进行调整
    # 运行 check_bom_table_fields.py 来获取准确的字段名
    
    return {
        # TVAS字段名 -> 飞书字段名
        "vin": "vin",
        "part_number": "part_number", 
        "part_revision": "part_revision",
        "en_name": "en_name",
        "cn_name": "cn_name", 
        "quantity": "quantity",
        "uom": "uom",
        "partition_code": "partition_code",
        "creator": "creator",
        "create_time": "create_time",
        "modifier": "modifier", 
        "modify_time": "modify_time",
        "status": "status"
    }


def convert_bom_record_safe(bom_record, vin):
    """
    安全地转换BOM记录格式
    :param bom_record: TVAS原始BOM记录
    :param vin: 车辆识别码
    :return: 格式化后的记录
    """
    field_mapping = get_safe_field_mapping()
    
    # 处理时间字段
    create_time_str = format_timestamp_to_string(bom_record.get("create_time"))
    modify_time_str = format_timestamp_to_string(bom_record.get("modify_time"))
    
    # 构建格式化记录
    formatted_record = {}
    
    # 基本字段映射
    for tvas_field, feishu_field in field_mapping.items():
        if tvas_field == "vin":
            formatted_record[feishu_field] = vin
        elif tvas_field == "create_time":
            formatted_record[feishu_field] = create_time_str
        elif tvas_field == "modify_time":
            formatted_record[feishu_field] = modify_time_str
        elif tvas_field == "quantity":
            # 确保数量字段为字符串
            formatted_record[feishu_field] = str(bom_record.get(tvas_field, ""))
        else:
            # 其他字段直接映射，确保为字符串
            value = bom_record.get(tvas_field, "")
            formatted_record[feishu_field] = str(value) if value is not None else ""
    
    return formatted_record


def test_single_record_insert(test_vin="HJNAAJNC8SA050133"):
    """
    测试单条记录插入，用于验证字段映射是否正确
    :param test_vin: 测试用的VIN
    """
    logger.info(f"开始测试VIN {test_vin} 的单条BOM记录插入")
    
    try:
        # 登录TVAS系统
        def login_system(url, element_id):
            login = login_nio.LoginNio(url, element_id)
            return login.get_cookie()
        
        tvas_cookie = login_system("https://tvas.nioint.com/tvas/vehicleResource", "container")
        tvas_client = get_tvas_info.GetTvasInfo(tvas_cookie)
        
        # 获取第一页BOM数据
        bom_data = tvas_client.query_bom_by_vin(test_vin, page_size=1, page_count=1)
        if not bom_data or not bom_data.get("list"):
            logger.error(f"无法获取VIN {test_vin} 的BOM数据")
            return False
        
        # 取第一条记录进行测试
        first_record = bom_data["list"][0]
        logger.info(f"原始BOM记录: {json.dumps(first_record, ensure_ascii=False, indent=2)}")
        
        # 转换格式
        formatted_record = convert_bom_record_safe(first_record, test_vin)
        logger.info(f"格式化记录: {json.dumps(formatted_record, ensure_ascii=False, indent=2)}")
        
        # 尝试插入到飞书表格
        bom_table = vehicle_sw_version_table.VehicleBom()
        
        logger.info("尝试插入单条记录到飞书表格...")
        success = bom_table.create_record(formatted_record)
        
        if success:
            logger.info("✅ 单条记录插入成功！")
            return True
        else:
            logger.error("❌ 单条记录插入失败")
            return False
            
    except Exception as e:
        logger.error(f"测试单条记录插入失败: {str(e)}")
        return False


def sync_bom_with_field_validation(test_vin="HJNAAJNC8SA050133", max_records=10):
    """
    带字段验证的BOM同步测试
    :param test_vin: 测试VIN
    :param max_records: 最大记录数（用于测试）
    """
    logger.info(f"开始带字段验证的BOM同步测试，VIN: {test_vin}, 最大记录数: {max_records}")
    
    try:
        # 登录TVAS系统
        def login_system(url, element_id):
            login = login_nio.LoginNio(url, element_id)
            return login.get_cookie()
        
        tvas_cookie = login_system("https://tvas.nioint.com/tvas/vehicleResource", "container")
        tvas_client = get_tvas_info.GetTvasInfo(tvas_cookie)
        
        # 获取BOM数据
        bom_data = tvas_client.query_bom_by_vin(test_vin, page_size=max_records, page_count=1)
        if not bom_data or not bom_data.get("list"):
            logger.error(f"无法获取VIN {test_vin} 的BOM数据")
            return
        
        records = bom_data["list"]
        logger.info(f"获取到 {len(records)} 条BOM记录")
        
        # 初始化BOM表格
        bom_table = vehicle_sw_version_table.VehicleBom()
        
        # 删除测试VIN的旧数据
        logger.info(f"删除VIN {test_vin} 的旧数据...")
        bom_table.delete_bom_records_by_vin(test_vin)
        
        # 逐条插入并验证
        success_count = 0
        for i, record in enumerate(records):
            try:
                formatted_record = convert_bom_record_safe(record, test_vin)
                
                logger.info(f"插入记录 {i+1}/{len(records)}: {formatted_record.get('part_number')} {formatted_record.get('part_revision')}")
                
                if bom_table.create_record(formatted_record):
                    success_count += 1
                    logger.info(f"✅ 记录 {i+1} 插入成功")
                else:
                    logger.error(f"❌ 记录 {i+1} 插入失败")
                    
            except Exception as e:
                logger.error(f"处理记录 {i+1} 时出错: {str(e)}")
        
        logger.info(f"同步完成: {success_count}/{len(records)} 条记录成功")
        
    except Exception as e:
        logger.error(f"带字段验证的BOM同步失败: {str(e)}")


def main():
    """主函数"""
    print("=" * 80)
    print("修复版本的BOM数据同步测试")
    print("=" * 80)
    
    # 测试VIN
    test_vin = "HJNAAJNC8SA050133"
    
    print(f"\n1. 测试单条记录插入 (VIN: {test_vin})")
    print("-" * 40)
    
    if test_single_record_insert(test_vin):
        print("✅ 单条记录测试成功，继续批量测试")
        
        print(f"\n2. 测试批量记录插入 (VIN: {test_vin}, 最多10条)")
        print("-" * 40)
        sync_bom_with_field_validation(test_vin, max_records=10)
    else:
        print("❌ 单条记录测试失败，请先解决字段映射问题")
        print("\n建议步骤:")
        print("1. 运行 python check_bom_table_fields.py 检查飞书表格字段")
        print("2. 根据检查结果修改 get_safe_field_mapping() 函数中的字段映射")
        print("3. 重新运行此测试脚本")


if __name__ == "__main__":
    main()
